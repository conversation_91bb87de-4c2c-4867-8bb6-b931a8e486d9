"""
NASA API Daily Pipeline DAG

This DAG runs daily to extract, transform, and load NASA API data.
It processes APOD, Mars rover photos, and NEO data.
"""

from datetime import datetime, timedelta
import sys
import os

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.sensors.filesystem import FileSensor
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup

from src.etl.pipeline import ETLPipeline
from src.database.manager import DatabaseManager
from src.config.settings import settings


# Default arguments for the DAG
default_args = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False,
}

# Create DAG
dag = DAG(
    'nasa_daily_pipeline',
    default_args=default_args,
    description='Daily NASA API data pipeline',
    schedule_interval='0 6 * * *',  # Run daily at 6 AM UTC
    max_active_runs=1,
    tags=['nasa', 'etl', 'daily'],
)


def check_database_health(**context):
    """Check database connectivity."""
    db_manager = DatabaseManager()
    if not db_manager.health_check():
        raise Exception("Database health check failed")
    return "Database is healthy"


def extract_apod_data(**context):
    """Extract APOD data for today."""
    pipeline = ETLPipeline()
    today = context['ds']  # Airflow execution date
    
    raw_data = pipeline.apod_extractor.extract_daily_apod(today)
    
    if not raw_data:
        raise Exception(f"No APOD data found for {today}")
    
    # Store in XCom for next task
    context['task_instance'].xcom_push(key='apod_raw_data', value=raw_data)
    return f"Extracted {len(raw_data)} APOD records"


def transform_apod_data(**context):
    """Transform APOD data."""
    pipeline = ETLPipeline()
    
    # Get raw data from previous task
    raw_data = context['task_instance'].xcom_pull(key='apod_raw_data')
    
    if not raw_data:
        raise Exception("No raw APOD data to transform")
    
    transformed_data = pipeline.apod_transformer.transform(raw_data)
    
    # Store in XCom for next task
    context['task_instance'].xcom_push(key='apod_transformed_data', value=transformed_data)
    return f"Transformed {len(transformed_data)} APOD records"


def load_apod_data(**context):
    """Load APOD data into database."""
    pipeline = ETLPipeline()
    
    # Get transformed data from previous task
    transformed_data = context['task_instance'].xcom_pull(key='apod_transformed_data')
    
    if not transformed_data:
        raise Exception("No transformed APOD data to load")
    
    inserted_count = pipeline.db_manager.insert_apod_data(transformed_data)
    return f"Loaded {inserted_count} APOD records"


def extract_neo_data(**context):
    """Extract NEO data for recent days."""
    pipeline = ETLPipeline()
    
    # Extract NEO data for last 3 days
    raw_data = pipeline.neo_extractor.extract_neo_feed(days_back=3)
    
    if not raw_data:
        raise Exception("No NEO data found")
    
    # Store in XCom for next task
    context['task_instance'].xcom_push(key='neo_raw_data', value=raw_data)
    return f"Extracted {len(raw_data)} NEO records"


def transform_neo_data(**context):
    """Transform NEO data."""
    pipeline = ETLPipeline()
    
    # Get raw data from previous task
    raw_data = context['task_instance'].xcom_pull(key='neo_raw_data')
    
    if not raw_data:
        raise Exception("No raw NEO data to transform")
    
    transformed_data = pipeline.neo_transformer.transform(raw_data)
    
    # Store in XCom for next task
    context['task_instance'].xcom_push(key='neo_transformed_data', value=transformed_data)
    return f"Transformed {len(transformed_data)} NEO records"


def load_neo_data(**context):
    """Load NEO data into database."""
    pipeline = ETLPipeline()
    
    # Get transformed data from previous task
    transformed_data = context['task_instance'].xcom_pull(key='neo_transformed_data')
    
    if not transformed_data:
        raise Exception("No transformed NEO data to load")
    
    inserted_count = pipeline.db_manager.insert_neo_data(transformed_data)
    return f"Loaded {inserted_count} NEO records"


def extract_mars_rover_data(**context):
    """Extract Mars rover data."""
    pipeline = ETLPipeline()
    
    rovers = ['curiosity', 'opportunity', 'spirit']
    all_photos = []
    
    for rover in rovers:
        try:
            # Update rover manifest
            manifest_data = pipeline.mars_extractor.extract_rover_manifest(rover)
            transformed_manifest = pipeline.mars_transformer.transform_rover_manifest(manifest_data)
            pipeline.db_manager.insert_rover_data(transformed_manifest)
            
            # Extract latest photos
            photos = pipeline.mars_extractor.extract_latest_rover_photos(rover, limit=25)
            all_photos.extend(photos)
            
        except Exception as e:
            print(f"Failed to extract data for {rover}: {e}")
            continue
    
    # Store in XCom for next task
    context['task_instance'].xcom_push(key='mars_raw_data', value=all_photos)
    return f"Extracted {len(all_photos)} Mars rover photos"


def transform_load_mars_data(**context):
    """Transform and load Mars rover data."""
    pipeline = ETLPipeline()
    
    # Get raw data from previous task
    raw_data = context['task_instance'].xcom_pull(key='mars_raw_data')
    
    if not raw_data:
        return "No Mars rover data to process"
    
    transformed_data = pipeline.mars_transformer.transform_photos(raw_data)
    
    if transformed_data:
        inserted_count = pipeline.db_manager.insert_rover_photos(transformed_data)
        return f"Loaded {inserted_count} Mars rover photos"
    
    return "No Mars rover photos to load"


def update_analytics(**context):
    """Update analytics tables."""
    pipeline = ETLPipeline()
    pipeline.update_analytics()
    return "Analytics updated successfully"


def generate_daily_report(**context):
    """Generate daily pipeline report."""
    execution_date = context['ds']
    
    # Get task results from XCom
    apod_result = context['task_instance'].xcom_pull(task_ids='load_apod_data')
    neo_result = context['task_instance'].xcom_pull(task_ids='load_neo_data')
    mars_result = context['task_instance'].xcom_pull(task_ids='transform_load_mars_data')
    
    report = f"""
    NASA Daily Pipeline Report - {execution_date}
    ================================================
    
    APOD: {apod_result or 'No data processed'}
    NEO: {neo_result or 'No data processed'}
    Mars Rovers: {mars_result or 'No data processed'}
    
    Pipeline completed successfully at {datetime.now()}
    """
    
    print(report)
    return report


# Define tasks
health_check_task = PythonOperator(
    task_id='health_check',
    python_callable=check_database_health,
    dag=dag,
)

# APOD Task Group
with TaskGroup('apod_pipeline', dag=dag) as apod_group:
    extract_apod_task = PythonOperator(
        task_id='extract_apod_data',
        python_callable=extract_apod_data,
    )
    
    transform_apod_task = PythonOperator(
        task_id='transform_apod_data',
        python_callable=transform_apod_data,
    )
    
    load_apod_task = PythonOperator(
        task_id='load_apod_data',
        python_callable=load_apod_data,
    )
    
    extract_apod_task >> transform_apod_task >> load_apod_task

# NEO Task Group
with TaskGroup('neo_pipeline', dag=dag) as neo_group:
    extract_neo_task = PythonOperator(
        task_id='extract_neo_data',
        python_callable=extract_neo_data,
    )
    
    transform_neo_task = PythonOperator(
        task_id='transform_neo_data',
        python_callable=transform_neo_data,
    )
    
    load_neo_task = PythonOperator(
        task_id='load_neo_data',
        python_callable=load_neo_data,
    )
    
    extract_neo_task >> transform_neo_task >> load_neo_task

# Mars Rover Task Group
with TaskGroup('mars_pipeline', dag=dag) as mars_group:
    extract_mars_task = PythonOperator(
        task_id='extract_mars_data',
        python_callable=extract_mars_rover_data,
    )
    
    transform_load_mars_task = PythonOperator(
        task_id='transform_load_mars_data',
        python_callable=transform_load_mars_data,
    )
    
    extract_mars_task >> transform_load_mars_task

# Analytics and reporting tasks
analytics_task = PythonOperator(
    task_id='update_analytics',
    python_callable=update_analytics,
    dag=dag,
)

report_task = PythonOperator(
    task_id='generate_report',
    python_callable=generate_daily_report,
    dag=dag,
)

# Define task dependencies
health_check_task >> [apod_group, neo_group, mars_group]
[apod_group, neo_group, mars_group] >> analytics_task >> report_task
