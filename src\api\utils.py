"""Utility functions for NASA API operations."""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Union
import requests
from PIL import Image
import io

from .data_models import APODData, MarsRoverPhoto, NEOObject, APIResponse


logger = logging.getLogger(__name__)


def validate_date_format(date_str: str) -> bool:
    """
    Validate date string format (YYYY-MM-DD).
    
    Args:
        date_str: Date string to validate
        
    Returns:
        True if valid format, False otherwise
    """
    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def get_date_range(days_back: int) -> tuple[str, str]:
    """
    Get date range for the last N days.
    
    Args:
        days_back: Number of days to go back
        
    Returns:
        Tuple of (start_date, end_date) in YYYY-MM-DD format
    """
    end_date = date.today()
    start_date = end_date - timedelta(days=days_back)
    
    return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")


def parse_apod_response(response_data: Union[Dict, List]) -> List[APODData]:
    """
    Parse APOD API response into structured data.
    
    Args:
        response_data: Raw API response data
        
    Returns:
        List of APODData objects
    """
    if isinstance(response_data, dict):
        response_data = [response_data]
    
    parsed_data = []
    for item in response_data:
        try:
            apod = APODData(**item)
            parsed_data.append(apod)
        except Exception as e:
            logger.error(f"Failed to parse APOD data: {e}")
            logger.debug(f"Problematic data: {item}")
    
    return parsed_data


def parse_mars_rover_response(response_data: Dict) -> List[MarsRoverPhoto]:
    """
    Parse Mars rover photos API response.
    
    Args:
        response_data: Raw API response data
        
    Returns:
        List of MarsRoverPhoto objects
    """
    photos = response_data.get("photos", [])
    parsed_photos = []
    
    for photo_data in photos:
        try:
            photo = MarsRoverPhoto(**photo_data)
            parsed_photos.append(photo)
        except Exception as e:
            logger.error(f"Failed to parse Mars rover photo: {e}")
            logger.debug(f"Problematic data: {photo_data}")
    
    return parsed_photos


def parse_neo_response(response_data: Dict) -> List[NEOObject]:
    """
    Parse NEO feed API response.
    
    Args:
        response_data: Raw API response data
        
    Returns:
        List of NEOObject objects
    """
    near_earth_objects = response_data.get("near_earth_objects", {})
    parsed_objects = []
    
    for date_key, objects in near_earth_objects.items():
        for obj_data in objects:
            try:
                neo = NEOObject(**obj_data)
                parsed_objects.append(neo)
            except Exception as e:
                logger.error(f"Failed to parse NEO object: {e}")
                logger.debug(f"Problematic data: {obj_data}")
    
    return parsed_objects


def download_image(url: str, max_size_mb: int = 10) -> Optional[bytes]:
    """
    Download image from URL with size validation.
    
    Args:
        url: Image URL
        max_size_mb: Maximum file size in MB
        
    Returns:
        Image bytes if successful, None otherwise
    """
    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        # Check content length
        content_length = response.headers.get('content-length')
        if content_length:
            size_mb = int(content_length) / (1024 * 1024)
            if size_mb > max_size_mb:
                logger.warning(f"Image too large: {size_mb:.2f}MB > {max_size_mb}MB")
                return None
        
        # Download image
        image_data = response.content
        
        # Validate it's actually an image
        try:
            Image.open(io.BytesIO(image_data))
        except Exception:
            logger.error("Downloaded content is not a valid image")
            return None
        
        return image_data
        
    except Exception as e:
        logger.error(f"Failed to download image from {url}: {e}")
        return None


def get_image_metadata(image_data: bytes) -> Dict[str, Any]:
    """
    Extract metadata from image data.
    
    Args:
        image_data: Image bytes
        
    Returns:
        Dictionary containing image metadata
    """
    try:
        image = Image.open(io.BytesIO(image_data))
        
        metadata = {
            "format": image.format,
            "mode": image.mode,
            "size": image.size,
            "width": image.width,
            "height": image.height,
        }
        
        # Add EXIF data if available
        if hasattr(image, '_getexif') and image._getexif():
            metadata["exif"] = dict(image._getexif())
        
        return metadata
        
    except Exception as e:
        logger.error(f"Failed to extract image metadata: {e}")
        return {}


def create_api_response(
    success: bool,
    data: Any = None,
    error: str = None,
    source: str = "NASA API"
) -> APIResponse:
    """
    Create standardized API response.
    
    Args:
        success: Whether the operation was successful
        data: Response data
        error: Error message if any
        source: Data source identifier
        
    Returns:
        APIResponse object
    """
    return APIResponse(
        success=success,
        data=data,
        error=error,
        source=source
    )


def filter_hazardous_neos(neo_objects: List[NEOObject]) -> List[NEOObject]:
    """
    Filter potentially hazardous NEOs.
    
    Args:
        neo_objects: List of NEO objects
        
    Returns:
        List of potentially hazardous NEOs
    """
    return [neo for neo in neo_objects if neo.is_potentially_hazardous_asteroid]


def get_rover_cameras() -> Dict[str, str]:
    """
    Get mapping of rover camera abbreviations to full names.
    
    Returns:
        Dictionary mapping camera codes to full names
    """
    return {
        "FHAZ": "Front Hazard Avoidance Camera",
        "RHAZ": "Rear Hazard Avoidance Camera",
        "MAST": "Mast Camera",
        "CHEMCAM": "Chemistry and Camera Complex",
        "MAHLI": "Mars Hand Lens Imager",
        "MARDI": "Mars Descent Imager",
        "NAVCAM": "Navigation Camera",
        "PANCAM": "Panoramic Camera",
        "MINITES": "Miniature Thermal Emission Spectrometer"
    }


def calculate_neo_approach_distance(neo: NEOObject) -> Optional[float]:
    """
    Calculate the closest approach distance for a NEO.
    
    Args:
        neo: NEO object
        
    Returns:
        Closest approach distance in kilometers, or None if no data
    """
    if not neo.close_approach_data:
        return None
    
    distances = []
    for approach in neo.close_approach_data:
        try:
            distance_km = float(approach.miss_distance["kilometers"])
            distances.append(distance_km)
        except (KeyError, ValueError):
            continue
    
    return min(distances) if distances else None
