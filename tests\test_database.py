"""Tests for database operations."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import date, datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.database.manager import DatabaseManager
from src.database.models import Base, APODModel, NEOObjectModel, MarsRoverModel


class TestDatabaseManager:
    """Test database manager functionality."""
    
    @pytest.fixture
    def mock_db_manager(self):
        """Create a mock database manager for testing."""
        with patch('src.database.manager.create_engine') as mock_engine:
            mock_session = MagicMock()
            with patch('src.database.manager.sessionmaker') as mock_sessionmaker:
                mock_sessionmaker.return_value = Mock(return_value=mock_session)
                db_manager = DatabaseManager("sqlite:///:memory:")
                db_manager.SessionLocal = Mock(return_value=mock_session)
                yield db_manager, mock_session
    
    def test_init(self):
        """Test database manager initialization."""
        db_manager = DatabaseManager("sqlite:///:memory:")
        assert db_manager.database_url == "sqlite:///:memory:"
        assert db_manager.engine is not None
        assert db_manager.SessionLocal is not None
    
    def test_health_check_success(self, mock_db_manager):
        """Test successful health check."""
        db_manager, mock_session = mock_db_manager
        
        with patch.object(db_manager.engine, 'connect') as mock_connect:
            mock_conn = Mock()
            mock_connect.return_value.__enter__.return_value = mock_conn
            
            result = db_manager.health_check()
            
            assert result is True
            mock_conn.execute.assert_called_once()
    
    def test_health_check_failure(self, mock_db_manager):
        """Test failed health check."""
        db_manager, mock_session = mock_db_manager
        
        with patch.object(db_manager.engine, 'connect') as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            result = db_manager.health_check()
            
            assert result is False
    
    def test_insert_apod_data_new_record(self, mock_db_manager):
        """Test inserting new APOD data."""
        db_manager, mock_session = mock_db_manager
        
        # Mock session behavior
        mock_session.query.return_value.filter_by.return_value.first.return_value = None
        
        apod_data = [{
            'date': date(2024, 1, 1),
            'title': 'Test APOD',
            'explanation': 'Test explanation',
            'url': 'https://example.com/image.jpg',
            'media_type': 'image'
        }]
        
        with patch.object(db_manager, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = db_manager.insert_apod_data(apod_data)
            
            assert result == 1
            mock_session.add.assert_called_once()
    
    def test_insert_apod_data_existing_record(self, mock_db_manager):
        """Test updating existing APOD data."""
        db_manager, mock_session = mock_db_manager
        
        # Mock existing record
        existing_record = APODModel(
            date=date(2024, 1, 1),
            title='Old Title',
            explanation='Old explanation',
            url='https://example.com/old.jpg',
            media_type='image'
        )
        mock_session.query.return_value.filter_by.return_value.first.return_value = existing_record
        
        apod_data = [{
            'date': date(2024, 1, 1),
            'title': 'Updated APOD',
            'explanation': 'Updated explanation',
            'url': 'https://example.com/new.jpg',
            'media_type': 'image'
        }]
        
        with patch.object(db_manager, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = db_manager.insert_apod_data(apod_data)
            
            assert result == 0  # No new records inserted
            assert existing_record.title == 'Updated APOD'
    
    def test_get_apod_by_date(self, mock_db_manager):
        """Test getting APOD by date."""
        db_manager, mock_session = mock_db_manager
        
        expected_apod = APODModel(
            date=date(2024, 1, 1),
            title='Test APOD',
            explanation='Test explanation',
            url='https://example.com/image.jpg',
            media_type='image'
        )
        mock_session.query.return_value.filter_by.return_value.first.return_value = expected_apod
        
        with patch.object(db_manager, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = db_manager.get_apod_by_date(date(2024, 1, 1))
            
            assert result == expected_apod
            mock_session.query.assert_called_with(APODModel)
    
    def test_insert_neo_data(self, mock_db_manager):
        """Test inserting NEO data."""
        db_manager, mock_session = mock_db_manager
        
        # Mock no existing record
        mock_session.query.return_value.filter_by.return_value.first.return_value = None
        
        neo_data = [{
            'neo_id': '123',
            'neo_reference_id': '123',
            'name': 'Test NEO',
            'nasa_jpl_url': 'https://example.com',
            'absolute_magnitude_h': 20.5,
            'estimated_diameter_min_km': 0.1,
            'estimated_diameter_max_km': 0.2,
            'is_potentially_hazardous': False,
            'close_approach_data': []
        }]
        
        with patch.object(db_manager, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = db_manager.insert_neo_data(neo_data)
            
            assert result == 1
            mock_session.add.assert_called()
    
    def test_get_neo_statistics(self, mock_db_manager):
        """Test getting NEO statistics."""
        db_manager, mock_session = mock_db_manager
        
        # Mock query results
        mock_session.query.return_value.count.side_effect = [100, 25]  # total, hazardous
        
        with patch.object(db_manager, 'get_session') as mock_get_session:
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            result = db_manager.get_neo_statistics()
            
            assert result['total_neo_count'] == 100
            assert result['hazardous_neo_count'] == 25
            assert result['hazardous_percentage'] == 25.0


class TestDatabaseModels:
    """Test database models."""
    
    def test_apod_model_creation(self):
        """Test APOD model creation."""
        apod = APODModel(
            date=date(2024, 1, 1),
            title='Test APOD',
            explanation='Test explanation',
            url='https://example.com/image.jpg',
            media_type='image'
        )
        
        assert apod.date == date(2024, 1, 1)
        assert apod.title == 'Test APOD'
        assert apod.media_type == 'image'
    
    def test_neo_model_creation(self):
        """Test NEO model creation."""
        neo = NEOObjectModel(
            neo_id='123',
            neo_reference_id='123',
            name='Test NEO',
            nasa_jpl_url='https://example.com',
            absolute_magnitude_h=20.5,
            is_potentially_hazardous=False
        )
        
        assert neo.neo_id == '123'
        assert neo.name == 'Test NEO'
        assert neo.is_potentially_hazardous is False
    
    def test_mars_rover_model_creation(self):
        """Test Mars rover model creation."""
        rover = MarsRoverModel(
            id=5,
            name='curiosity',
            landing_date=date(2012, 8, 5),
            launch_date=date(2011, 11, 26),
            status='active'
        )
        
        assert rover.name == 'curiosity'
        assert rover.status == 'active'
        assert rover.landing_date == date(2012, 8, 5)


@pytest.fixture
def in_memory_db():
    """Create an in-memory SQLite database for testing."""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


class TestDatabaseIntegration:
    """Integration tests with real database operations."""
    
    def test_create_and_query_apod(self, in_memory_db):
        """Test creating and querying APOD records."""
        session = in_memory_db
        
        # Create APOD record
        apod = APODModel(
            date=date(2024, 1, 1),
            title='Test APOD',
            explanation='Test explanation',
            url='https://example.com/image.jpg',
            media_type='image'
        )
        
        session.add(apod)
        session.commit()
        
        # Query the record
        retrieved = session.query(APODModel).filter_by(date=date(2024, 1, 1)).first()
        
        assert retrieved is not None
        assert retrieved.title == 'Test APOD'
        assert retrieved.media_type == 'image'
    
    def test_create_and_query_neo(self, in_memory_db):
        """Test creating and querying NEO records."""
        session = in_memory_db
        
        # Create NEO record
        neo = NEOObjectModel(
            neo_id='123',
            neo_reference_id='123',
            name='Test NEO',
            nasa_jpl_url='https://example.com',
            absolute_magnitude_h=20.5,
            is_potentially_hazardous=True
        )
        
        session.add(neo)
        session.commit()
        
        # Query the record
        retrieved = session.query(NEOObjectModel).filter_by(neo_id='123').first()
        
        assert retrieved is not None
        assert retrieved.name == 'Test NEO'
        assert retrieved.is_potentially_hazardous is True
    
    def test_hazardous_neo_query(self, in_memory_db):
        """Test querying hazardous NEOs."""
        session = in_memory_db
        
        # Create multiple NEO records
        neo1 = NEOObjectModel(neo_id='1', name='Safe NEO', is_potentially_hazardous=False)
        neo2 = NEOObjectModel(neo_id='2', name='Hazardous NEO', is_potentially_hazardous=True)
        
        session.add_all([neo1, neo2])
        session.commit()
        
        # Query hazardous NEOs
        hazardous = session.query(NEOObjectModel).filter(
            NEOObjectModel.is_potentially_hazardous == True
        ).all()
        
        assert len(hazardous) == 1
        assert hazardous[0].name == 'Hazardous NEO'
