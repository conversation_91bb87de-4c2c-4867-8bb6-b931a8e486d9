[project]
name = "nasa-api-pipeline"
version = "0.1.0"
description = "NASA API Data Pipeline with Apache Airflow, PostgreSQL, and Streamlit"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "requests>=2.31.0",
    "pandas>=2.1.0",
    "sqlalchemy>=2.0.0",
    "psycopg2-binary>=2.9.0",
    "streamlit>=1.28.0",
    "plotly>=5.17.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.4.0",
    "pydantic-settings>=2.0.0",
    "schedule>=1.2.0",
    "pillow>=10.0.0",
    "apache-airflow>=2.7.0",
    "apache-airflow-providers-postgres>=5.6.0",
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
