# NASA API Pipeline - API Documentation

This document provides comprehensive documentation for the NASA API integration and internal pipeline APIs.

## 🌌 NASA APIs Used

### 1. Astronomy Picture of the Day (APOD)

**Endpoint**: `https://api.nasa.gov/planetary/apod`

**Description**: Daily astronomy images and explanations from NASA.

**Parameters**:
- `api_key` (string): NASA API key
- `date` (string, optional): YYYY-MM-DD format for specific date
- `start_date` (string, optional): YYYY-MM-DD format for date range start
- `end_date` (string, optional): YYYY-MM-DD format for date range end
- `count` (integer, optional): Number of random images (max 100)
- `thumbs` (boolean, optional): Return thumbnail URL for videos

**Example Response**:
```json
{
  "date": "2024-01-01",
  "explanation": "What's happening to galaxy NGC 474?",
  "hdurl": "https://apod.nasa.gov/apod/image/2401/NGC474_hst.jpg",
  "media_type": "image",
  "service_version": "v1",
  "title": "Galaxy NGC 474: Shells and Star Streams",
  "url": "https://apod.nasa.gov/apod/image/2401/NGC474_hst1024.jpg"
}
```

### 2. Mars Rover Photos

**Endpoint**: `https://api.nasa.gov/mars-photos/api/v1/rovers/{rover}/photos`

**Description**: Photos from Mars rovers (Curiosity, Opportunity, Spirit).

**Parameters**:
- `api_key` (string): NASA API key
- `sol` (integer): Martian sol (day) number
- `earth_date` (string): Earth date in YYYY-MM-DD format
- `camera` (string, optional): Camera abbreviation
- `page` (integer): Page number for pagination

**Available Cameras**:
- `FHAZ`: Front Hazard Avoidance Camera
- `RHAZ`: Rear Hazard Avoidance Camera
- `MAST`: Mast Camera
- `CHEMCAM`: Chemistry and Camera Complex
- `MAHLI`: Mars Hand Lens Imager
- `MARDI`: Mars Descent Imager
- `NAVCAM`: Navigation Camera
- `PANCAM`: Panoramic Camera (Opportunity/Spirit)
- `MINITES`: Miniature Thermal Emission Spectrometer

**Example Response**:
```json
{
  "photos": [
    {
      "id": 102693,
      "sol": 1000,
      "camera": {
        "id": 20,
        "name": "FHAZ",
        "rover_id": 5,
        "full_name": "Front Hazard Avoidance Camera"
      },
      "img_src": "http://mars.jpl.nasa.gov/msl-raw-images/proj/msl/redops/ods/surface/sol/01000/opgs/edr/fcam/FLB_486265257EDR_F0481570FHAZ00323M_.JPG",
      "earth_date": "2015-05-30",
      "rover": {
        "id": 5,
        "name": "Curiosity",
        "landing_date": "2012-08-05",
        "launch_date": "2011-11-26",
        "status": "active"
      }
    }
  ]
}
```

### 3. Near Earth Objects (NEO)

**Endpoint**: `https://api.nasa.gov/neo/rest/v1/feed`

**Description**: Near Earth Objects approaching Earth.

**Parameters**:
- `api_key` (string): NASA API key
- `start_date` (string, optional): YYYY-MM-DD format (default: today)
- `end_date` (string, optional): YYYY-MM-DD format (default: today + 7 days)

**Example Response**:
```json
{
  "links": {
    "next": "http://www.neowsapi.com/rest/v1/feed?start_date=2015-09-08&end_date=2015-09-09&detailed=false&api_key=DEMO_KEY",
    "prev": "http://www.neowsapi.com/rest/v1/feed?start_date=2015-09-06&end_date=2015-09-07&detailed=false&api_key=DEMO_KEY",
    "self": "http://www.neowsapi.com/rest/v1/feed?start_date=2015-09-07&end_date=2015-09-08&detailed=false&api_key=DEMO_KEY"
  },
  "element_count": 25,
  "near_earth_objects": {
    "2015-09-07": [
      {
        "links": {
          "self": "http://www.neowsapi.com/rest/v1/neo/3726710?api_key=DEMO_KEY"
        },
        "id": "3726710",
        "neo_reference_id": "3726710",
        "name": "(2015 RC)",
        "nasa_jpl_url": "http://ssd.jpl.nasa.gov/sbdb.cgi?sstr=3726710",
        "absolute_magnitude_h": 24.3,
        "estimated_diameter": {
          "kilometers": {
            "estimated_diameter_min": 0.0366906138,
            "estimated_diameter_max": 0.0820427065
          }
        },
        "is_potentially_hazardous_asteroid": false,
        "close_approach_data": [
          {
            "close_approach_date": "2015-09-07",
            "close_approach_date_full": "2015-Sep-07 20:28",
            "epoch_date_close_approach": 1441655280000,
            "relative_velocity": {
              "kilometers_per_second": "19.4850295284",
              "kilometers_per_hour": "70146.1062822",
              "miles_per_hour": "43581.395662"
            },
            "miss_distance": {
              "astronomical": "0.0269230273",
              "lunar": "10.4730616197",
              "kilometers": "4027634.7066662",
              "miles": "2502463.312"
            },
            "orbiting_body": "Earth"
          }
        ],
        "is_sentry_object": false
      }
    ]
  }
}
```

## 🔧 Internal Pipeline APIs

### Pipeline Control API

The main entry point provides several commands for pipeline control:

```bash
# Run complete pipeline
uv run main.py pipeline

# Extract data only
uv run main.py extract

# Transform data only
uv run main.py transform

# Load data only
uv run main.py load

# Setup database
uv run main.py setup

# Launch dashboard
uv run main.py dashboard
```

### ETL Pipeline Classes

#### APODExtractor
```python
from src.etl.extractors import APODExtractor

extractor = APODExtractor()

# Extract single date
data = extractor.extract_daily_apod("2024-01-01")

# Extract date range
data = extractor.extract_apod_range(days_back=7)
```

#### MarsRoverExtractor
```python
from src.etl.extractors import MarsRoverExtractor

extractor = MarsRoverExtractor()

# Extract rover photos
photos = extractor.extract_rover_photos(
    rover="curiosity",
    sol=1000,
    camera="FHAZ"
)

# Extract rover manifest
manifest = extractor.extract_rover_manifest("curiosity")

# Extract latest photos
latest = extractor.extract_latest_rover_photos("curiosity", limit=25)
```

#### NEOExtractor
```python
from src.etl.extractors import NEOExtractor

extractor = NEOExtractor()

# Extract NEO feed
neos = extractor.extract_neo_feed(
    start_date="2024-01-01",
    end_date="2024-01-07"
)

# Extract recent NEOs
recent = extractor.extract_recent_neo_feed(days=7)

# Extract NEO statistics
stats = extractor.extract_neo_stats()
```

### Database API

#### DatabaseManager
```python
from src.database.manager import DatabaseManager

db = DatabaseManager()

# Health check
healthy = db.health_check()

# Insert APOD data
count = db.insert_apod_data(apod_data_list)

# Get APOD by date
apod = db.get_apod_by_date(date(2024, 1, 1))

# Insert NEO data
count = db.insert_neo_data(neo_data_list)

# Get hazardous NEOs
hazardous = db.get_hazardous_neos()

# Get NEO statistics
stats = db.get_neo_statistics()
```

## 📊 Data Models

### APOD Data Model
```python
{
    'date': date,           # Date of the APOD
    'title': str,           # Title of the image/video
    'explanation': str,     # Detailed explanation
    'url': str,            # URL to the media
    'hdurl': str,          # High-resolution URL (optional)
    'media_type': str,     # 'image' or 'video'
    'copyright': str,      # Copyright information (optional)
    'service_version': str # API service version
}
```

### Mars Rover Photo Model
```python
{
    'photo_id': int,       # Unique photo ID
    'sol': int,           # Martian sol number
    'earth_date': date,   # Earth date of photo
    'img_src': str,       # URL to the image
    'rover_id': int,      # Rover ID
    'camera_id': int,     # Camera ID
    'rover': {            # Rover information
        'id': int,
        'name': str,
        'landing_date': date,
        'launch_date': date,
        'status': str
    },
    'camera': {           # Camera information
        'id': int,
        'name': str,
        'full_name': str,
        'rover_id': int
    }
}
```

### NEO Data Model
```python
{
    'neo_id': str,                    # NEO ID
    'neo_reference_id': str,          # NEO reference ID
    'name': str,                      # NEO name
    'nasa_jpl_url': str,             # NASA JPL URL
    'absolute_magnitude_h': float,    # Absolute magnitude
    'estimated_diameter_min_km': float, # Min diameter in km
    'estimated_diameter_max_km': float, # Max diameter in km
    'is_potentially_hazardous': bool,   # Hazardous flag
    'close_approach_data': [          # Close approach information
        {
            'close_approach_date': date,
            'relative_velocity_kmh': float,
            'miss_distance_km': float,
            'orbiting_body': str
        }
    ]
}
```

## 🔄 API Rate Limits and Best Practices

### NASA API Rate Limits
- **Demo Key**: 30 requests per hour, 50 requests per day
- **API Key**: 1000 requests per hour

### Best Practices
1. **Use API Key**: Always use your personal API key for production
2. **Implement Caching**: Cache responses to reduce API calls
3. **Respect Rate Limits**: Implement proper rate limiting
4. **Error Handling**: Handle API errors gracefully
5. **Retry Logic**: Implement exponential backoff for retries

### Rate Limiting Implementation
```python
from src.api.base_client import BaseAPIClient

# Rate limiting is automatically handled
client = BaseAPIClient(
    base_url="https://api.nasa.gov",
    api_key="your_api_key"
)

# Requests are automatically rate limited
response = client._make_request("planetary/apod")
```

## 🚨 Error Handling

### Common Error Codes
- `400`: Bad Request - Invalid parameters
- `403`: Forbidden - Invalid API key or rate limit exceeded
- `404`: Not Found - Resource not found
- `500`: Internal Server Error - NASA API issues

### Error Response Format
```json
{
    "error": {
        "code": "OVER_RATE_LIMIT",
        "message": "You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance"
    }
}
```

### Handling Errors in Code
```python
try:
    response = client.get_apod(date="2024-01-01")
except requests.exceptions.HTTPError as e:
    if e.response.status_code == 429:
        # Rate limit exceeded
        time.sleep(60)  # Wait and retry
    elif e.response.status_code == 403:
        # Invalid API key
        logger.error("Invalid API key")
    else:
        # Other HTTP errors
        logger.error(f"HTTP error: {e}")
except requests.exceptions.RequestException as e:
    # Network or other request errors
    logger.error(f"Request failed: {e}")
```

## 📝 API Testing

### Unit Tests
```python
# Test API client
pytest tests/test_api_clients.py

# Test specific functionality
pytest tests/test_api_clients.py::TestNASAClient::test_get_apod_single_date
```

### Integration Tests
```python
# Test with real API (requires internet)
pytest tests/test_api_clients.py -m integration
```

### Manual Testing
```python
from src.api.nasa_client import NASAClient

client = NASAClient()

# Test APOD
apod = client.get_apod(date="2024-01-01")
print(apod)

# Test Mars photos
photos = client.get_mars_rover_photos(rover="curiosity", sol=1000)
print(photos)

# Test NEO feed
neos = client.get_neo_feed(start_date="2024-01-01", end_date="2024-01-01")
print(neos)
```
