@echo off
REM NASA API Pipeline Setup Script for Windows
REM This script sets up the development environment on Windows

echo.
echo 🚀 NASA API Pipeline Setup
echo ==========================
echo.

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop and try again.
    pause
    exit /b 1
)

echo ✅ Docker is running

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.11+ and try again.
    pause
    exit /b 1
)

echo ✅ Python is available

REM Check if UV is installed
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installing UV package manager...
    pip install uv
    if %errorlevel% neq 0 (
        echo ❌ Failed to install UV package manager
        pause
        exit /b 1
    )
)

echo ✅ UV package manager is available

REM Create .env file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your NASA API key and other configurations
)

REM Create necessary directories
echo 📁 Creating directories...
if not exist data mkdir data
if not exist logs mkdir logs
if not exist notebooks mkdir notebooks

REM Install Python dependencies
echo 📦 Installing Python dependencies...
uv sync
if %errorlevel% neq 0 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
)

REM Build Docker images
echo 🐳 Building Docker images...
docker-compose build
if %errorlevel% neq 0 (
    echo ❌ Failed to build Docker images
    pause
    exit /b 1
)

REM Start PostgreSQL for initial setup
echo 🗄️  Starting PostgreSQL...
docker-compose up postgres -d
if %errorlevel% neq 0 (
    echo ❌ Failed to start PostgreSQL
    pause
    exit /b 1
)

REM Wait for PostgreSQL to be ready
echo ⏳ Waiting for PostgreSQL to be ready...
timeout /t 15 /nobreak >nul

REM Initialize database
echo 🗄️  Initializing database...
uv run main.py setup
if %errorlevel% neq 0 (
    echo ⚠️  Database initialization had issues, but continuing...
)

echo.
echo ✅ Setup completed successfully!
echo.
echo Next steps:
echo 1. Edit .env file with your NASA API key
echo 2. Start all services: docker-compose up -d
echo 3. Access applications:
echo    - Airflow: http://localhost:8080 (admin/admin)
echo    - Streamlit: http://localhost:8501
echo    - PostgreSQL: localhost:5432
echo.
echo 4. Run the pipeline: uv run main.py pipeline
echo.
echo For development:
echo - Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
echo - Jupyter Lab will be available at: http://localhost:8888
echo.
pause
