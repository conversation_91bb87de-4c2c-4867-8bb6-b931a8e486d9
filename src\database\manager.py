"""Database manager for NASA API pipeline."""

import logging
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from src.config.settings import settings
from .models import (
    Base, APODModel, MarsRoverModel, MarsRoverCameraModel, 
    MarsRoverPhotoModel, NEOObjectModel, NEOCloseApproachModel,
    DailySummaryModel, PopularCameraModel, NEOStatisticsModel
)


class DatabaseManager:
    """Database manager for NASA API data."""
    
    def __init__(self, database_url: Optional[str] = None):
        self.database_url = database_url or settings.database_url
        self.engine = create_engine(
            self.database_url,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=False  # Set to True for SQL debugging
        )
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.logger = logging.getLogger(__name__)
    
    @contextmanager
    def get_session(self):
        """Get database session with automatic cleanup."""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self):
        """Create all database tables."""
        try:
            # Create schemas first
            with self.engine.connect() as conn:
                conn.execute(text("CREATE SCHEMA IF NOT EXISTS raw_data"))
                conn.execute(text("CREATE SCHEMA IF NOT EXISTS processed_data"))
                conn.execute(text("CREATE SCHEMA IF NOT EXISTS analytics"))
                conn.commit()
            
            # Create tables
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("Database tables created successfully")
            
        except SQLAlchemyError as e:
            self.logger.error(f"Failed to create tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables."""
        try:
            Base.metadata.drop_all(bind=self.engine)
            self.logger.info("Database tables dropped successfully")
        except SQLAlchemyError as e:
            self.logger.error(f"Failed to drop tables: {e}")
            raise
    
    def health_check(self) -> bool:
        """Check database connectivity."""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return False
    
    # APOD Methods
    def insert_apod_data(self, apod_data: List[Dict[str, Any]]) -> int:
        """Insert APOD data into database."""
        inserted_count = 0
        
        with self.get_session() as session:
            for item in apod_data:
                try:
                    # Check if record already exists
                    existing = session.query(APODModel).filter_by(date=item['date']).first()
                    if existing:
                        # Update existing record
                        for key, value in item.items():
                            setattr(existing, key, value)
                        existing.updated_at = datetime.now()
                    else:
                        # Create new record
                        apod = APODModel(**item)
                        session.add(apod)
                        inserted_count += 1
                        
                except Exception as e:
                    self.logger.error(f"Failed to insert APOD data: {e}")
                    continue
        
        self.logger.info(f"Inserted {inserted_count} APOD records")
        return inserted_count
    
    def get_apod_by_date(self, target_date: date) -> Optional[APODModel]:
        """Get APOD data for specific date."""
        with self.get_session() as session:
            return session.query(APODModel).filter_by(date=target_date).first()
    
    def get_recent_apod(self, days: int = 7) -> List[APODModel]:
        """Get recent APOD data."""
        with self.get_session() as session:
            return (session.query(APODModel)
                   .order_by(APODModel.date.desc())
                   .limit(days)
                   .all())
    
    # Mars Rover Methods
    def insert_rover_data(self, rover_data: Dict[str, Any]) -> int:
        """Insert or update rover data."""
        with self.get_session() as session:
            existing = session.query(MarsRoverModel).filter_by(id=rover_data['id']).first()
            if existing:
                for key, value in rover_data.items():
                    setattr(existing, key, value)
                existing.updated_at = datetime.now()
                return 0
            else:
                rover = MarsRoverModel(**rover_data)
                session.add(rover)
                return 1
    
    def insert_rover_photos(self, photos_data: List[Dict[str, Any]]) -> int:
        """Insert Mars rover photos."""
        inserted_count = 0
        
        with self.get_session() as session:
            for photo_data in photos_data:
                try:
                    # Check if photo already exists
                    existing = session.query(MarsRoverPhotoModel).filter_by(
                        photo_id=photo_data['photo_id']
                    ).first()
                    
                    if not existing:
                        photo = MarsRoverPhotoModel(**photo_data)
                        session.add(photo)
                        inserted_count += 1
                        
                except Exception as e:
                    self.logger.error(f"Failed to insert rover photo: {e}")
                    continue
        
        self.logger.info(f"Inserted {inserted_count} rover photos")
        return inserted_count
    
    def get_rover_photos_by_sol(self, rover_name: str, sol: int) -> List[MarsRoverPhotoModel]:
        """Get rover photos for specific sol."""
        with self.get_session() as session:
            return (session.query(MarsRoverPhotoModel)
                   .join(MarsRoverModel)
                   .filter(MarsRoverModel.name == rover_name)
                   .filter(MarsRoverPhotoModel.sol == sol)
                   .all())
    
    # NEO Methods
    def insert_neo_data(self, neo_data: List[Dict[str, Any]]) -> int:
        """Insert NEO data."""
        inserted_count = 0
        
        with self.get_session() as session:
            for neo_item in neo_data:
                try:
                    # Check if NEO already exists
                    existing = session.query(NEOObjectModel).filter_by(
                        neo_id=neo_item['neo_id']
                    ).first()
                    
                    if existing:
                        # Update existing record
                        for key, value in neo_item.items():
                            if key != 'close_approach_data':
                                setattr(existing, key, value)
                        existing.updated_at = datetime.now()
                    else:
                        # Create new NEO record
                        neo_obj = NEOObjectModel(**{k: v for k, v in neo_item.items() 
                                                  if k != 'close_approach_data'})
                        session.add(neo_obj)
                        session.flush()  # Get the ID
                        
                        # Insert close approach data
                        for approach_data in neo_item.get('close_approach_data', []):
                            approach_data['neo_object_id'] = neo_obj.id
                            approach = NEOCloseApproachModel(**approach_data)
                            session.add(approach)
                        
                        inserted_count += 1
                        
                except Exception as e:
                    self.logger.error(f"Failed to insert NEO data: {e}")
                    continue
        
        self.logger.info(f"Inserted {inserted_count} NEO records")
        return inserted_count
    
    def get_hazardous_neos(self) -> List[NEOObjectModel]:
        """Get potentially hazardous NEOs."""
        with self.get_session() as session:
            return (session.query(NEOObjectModel)
                   .filter(NEOObjectModel.is_potentially_hazardous == True)
                   .all())
    
    def get_neo_statistics(self) -> Dict[str, Any]:
        """Get NEO statistics."""
        with self.get_session() as session:
            total_count = session.query(NEOObjectModel).count()
            hazardous_count = session.query(NEOObjectModel).filter(
                NEOObjectModel.is_potentially_hazardous == True
            ).count()
            
            return {
                'total_neo_count': total_count,
                'hazardous_neo_count': hazardous_count,
                'hazardous_percentage': (hazardous_count / total_count * 100) if total_count > 0 else 0
            }
    
    # Analytics Methods
    def update_daily_summary(self, target_date: date) -> None:
        """Update daily summary for given date."""
        with self.get_session() as session:
            # Get counts for the date
            apod_count = session.query(APODModel).filter_by(date=target_date).count()
            
            mars_photos_count = session.query(MarsRoverPhotoModel).filter_by(
                earth_date=target_date
            ).count()
            
            neo_count = session.query(NEOCloseApproachModel).filter_by(
                close_approach_date=target_date
            ).count()
            
            hazardous_neo_count = (session.query(NEOCloseApproachModel)
                                 .join(NEOObjectModel)
                                 .filter(NEOCloseApproachModel.close_approach_date == target_date)
                                 .filter(NEOObjectModel.is_potentially_hazardous == True)
                                 .count())
            
            # Update or create summary
            summary = session.query(DailySummaryModel).filter_by(date=target_date).first()
            if summary:
                summary.apod_count = apod_count
                summary.mars_photos_count = mars_photos_count
                summary.neo_count = neo_count
                summary.hazardous_neo_count = hazardous_neo_count
                summary.updated_at = datetime.now()
            else:
                summary = DailySummaryModel(
                    date=target_date,
                    apod_count=apod_count,
                    mars_photos_count=mars_photos_count,
                    neo_count=neo_count,
                    hazardous_neo_count=hazardous_neo_count
                )
                session.add(summary)
