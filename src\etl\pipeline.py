"""Main ETL pipeline orchestrator."""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import time

from src.database.manager import DatabaseManager
from .extractors import APODExtractor, MarsRoverExtractor, NEOExtractor
from .transformers import APODTransformer, MarsRoverTransformer, NEOTransformer, DataQualityChecker
from src.config.settings import settings


class ETLPipeline:
    """Main ETL pipeline orchestrator."""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.apod_extractor = APODExtractor()
        self.mars_extractor = MarsRoverExtractor()
        self.neo_extractor = NEOExtractor()
        self.apod_transformer = APODTransformer()
        self.mars_transformer = MarsRoverTransformer()
        self.neo_transformer = NEOTransformer()
        self.quality_checker = DataQualityChecker()
        self.logger = logging.getLogger(__name__)
    
    def run_full_pipeline(self, days_back: int = 7) -> Dict[str, Any]:
        """Run the complete ETL pipeline."""
        self.logger.info(f"Starting full ETL pipeline for last {days_back} days")
        start_time = time.time()
        
        results = {
            'start_time': datetime.now(),
            'apod_results': {},
            'mars_results': {},
            'neo_results': {},
            'errors': [],
            'total_records_processed': 0
        }
        
        try:
            # Run APOD pipeline
            self.logger.info("Running APOD pipeline...")
            results['apod_results'] = self.run_apod_pipeline(days_back)
            
            # Run Mars rover pipeline
            self.logger.info("Running Mars rover pipeline...")
            results['mars_results'] = self.run_mars_pipeline()
            
            # Run NEO pipeline
            self.logger.info("Running NEO pipeline...")
            results['neo_results'] = self.run_neo_pipeline(days_back)
            
            # Update analytics
            self.logger.info("Updating analytics...")
            self.update_analytics()
            
            # Calculate totals
            results['total_records_processed'] = (
                results['apod_results'].get('records_inserted', 0) +
                results['mars_results'].get('records_inserted', 0) +
                results['neo_results'].get('records_inserted', 0)
            )
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            results['errors'].append(str(e))
        
        results['end_time'] = datetime.now()
        results['duration_seconds'] = time.time() - start_time
        
        self.logger.info(f"Pipeline completed in {results['duration_seconds']:.2f} seconds")
        self.logger.info(f"Total records processed: {results['total_records_processed']}")
        
        return results
    
    def run_apod_pipeline(self, days_back: int = 7) -> Dict[str, Any]:
        """Run APOD ETL pipeline."""
        results = {
            'records_extracted': 0,
            'records_transformed': 0,
            'records_inserted': 0,
            'quality_score': 0.0,
            'errors': []
        }
        
        try:
            # Extract
            raw_data = self.apod_extractor.extract_apod_range(days_back)
            results['records_extracted'] = len(raw_data)
            
            if not raw_data:
                self.logger.warning("No APOD data extracted")
                return results
            
            # Transform
            transformed_data = self.apod_transformer.transform(raw_data)
            results['records_transformed'] = len(transformed_data)
            
            # Quality check
            quality_report = self.quality_checker.check_data_completeness(
                transformed_data, ['date', 'title', 'url']
            )
            results['quality_score'] = quality_report['completeness_score']
            
            # Load
            if transformed_data:
                inserted_count = self.db_manager.insert_apod_data(transformed_data)
                results['records_inserted'] = inserted_count
            
        except Exception as e:
            error_msg = f"APOD pipeline failed: {e}"
            self.logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def run_mars_pipeline(self) -> Dict[str, Any]:
        """Run Mars rover ETL pipeline."""
        results = {
            'records_extracted': 0,
            'records_transformed': 0,
            'records_inserted': 0,
            'rovers_processed': [],
            'errors': []
        }
        
        rovers = ['curiosity', 'opportunity', 'spirit']
        
        for rover in rovers:
            try:
                self.logger.info(f"Processing {rover} rover data")
                
                # Extract and update rover manifest
                manifest_data = self.mars_extractor.extract_rover_manifest(rover)
                transformed_manifest = self.mars_transformer.transform_rover_manifest(manifest_data)
                self.db_manager.insert_rover_data(transformed_manifest)
                
                # Extract latest photos
                raw_photos = self.mars_extractor.extract_latest_rover_photos(rover, limit=50)
                results['records_extracted'] += len(raw_photos)
                
                if raw_photos:
                    # Transform photos
                    transformed_photos = self.mars_transformer.transform_photos(raw_photos)
                    results['records_transformed'] += len(transformed_photos)
                    
                    # Load photos
                    if transformed_photos:
                        inserted_count = self.db_manager.insert_rover_photos(transformed_photos)
                        results['records_inserted'] += inserted_count
                
                results['rovers_processed'].append(rover)
                
            except Exception as e:
                error_msg = f"Mars rover {rover} pipeline failed: {e}"
                self.logger.error(error_msg)
                results['errors'].append(error_msg)
        
        return results
    
    def run_neo_pipeline(self, days_back: int = 7) -> Dict[str, Any]:
        """Run NEO ETL pipeline."""
        results = {
            'records_extracted': 0,
            'records_transformed': 0,
            'records_inserted': 0,
            'hazardous_objects': 0,
            'quality_score': 0.0,
            'errors': []
        }
        
        try:
            # Extract
            end_date = date.today()
            start_date = end_date - timedelta(days=days_back)
            
            raw_data = self.neo_extractor.extract_neo_feed(
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            results['records_extracted'] = len(raw_data)
            
            if not raw_data:
                self.logger.warning("No NEO data extracted")
                return results
            
            # Transform
            transformed_data = self.neo_transformer.transform(raw_data)
            results['records_transformed'] = len(transformed_data)
            
            # Count hazardous objects
            results['hazardous_objects'] = sum(
                1 for neo in transformed_data 
                if neo.get('is_potentially_hazardous', False)
            )
            
            # Quality check
            quality_report = self.quality_checker.check_data_completeness(
                transformed_data, ['neo_id', 'name', 'absolute_magnitude_h']
            )
            results['quality_score'] = quality_report['completeness_score']
            
            # Load
            if transformed_data:
                inserted_count = self.db_manager.insert_neo_data(transformed_data)
                results['records_inserted'] = inserted_count
            
        except Exception as e:
            error_msg = f"NEO pipeline failed: {e}"
            self.logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def update_analytics(self) -> None:
        """Update analytics tables."""
        try:
            # Update daily summary for today
            today = date.today()
            self.db_manager.update_daily_summary(today)
            
            # Update daily summary for yesterday (in case of late data)
            yesterday = today - timedelta(days=1)
            self.db_manager.update_daily_summary(yesterday)
            
            self.logger.info("Analytics updated successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to update analytics: {e}")
    
    def run_daily_pipeline(self) -> Dict[str, Any]:
        """Run daily pipeline for current date."""
        self.logger.info("Running daily pipeline")
        
        results = {
            'date': date.today(),
            'apod_results': {},
            'neo_results': {},
            'errors': []
        }
        
        try:
            # Extract today's APOD
            today_str = date.today().strftime("%Y-%m-%d")
            raw_apod = self.apod_extractor.extract_daily_apod(today_str)
            
            if raw_apod:
                transformed_apod = self.apod_transformer.transform(raw_apod)
                if transformed_apod:
                    inserted_count = self.db_manager.insert_apod_data(transformed_apod)
                    results['apod_results']['records_inserted'] = inserted_count
            
            # Extract recent NEO data (last 3 days)
            raw_neo = self.neo_extractor.extract_neo_feed(days_back=3)
            
            if raw_neo:
                transformed_neo = self.neo_transformer.transform(raw_neo)
                if transformed_neo:
                    inserted_count = self.db_manager.insert_neo_data(transformed_neo)
                    results['neo_results']['records_inserted'] = inserted_count
            
            # Update analytics
            self.update_analytics()
            
        except Exception as e:
            error_msg = f"Daily pipeline failed: {e}"
            self.logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def health_check(self) -> Dict[str, bool]:
        """Perform health check on all pipeline components."""
        health_status = {}
        
        try:
            # Database health
            health_status['database'] = self.db_manager.health_check()
            
            # API health
            health_status['nasa_api'] = self.apod_extractor.client.health_check()
            
            # Overall health
            health_status['overall'] = all(health_status.values())
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            health_status['overall'] = False
        
        return health_status
