"""Data models for NASA API responses."""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


class APODData(BaseModel):
    """Astronomy Picture of the Day data model."""
    
    date: date
    title: str
    explanation: str
    url: str
    hdurl: Optional[str] = None
    media_type: str
    copyright: Optional[str] = None
    service_version: Optional[str] = None
    
    @validator('date', pre=True)
    def parse_date(cls, v):
        if isinstance(v, str):
            return datetime.strptime(v, "%Y-%m-%d").date()
        return v


class MarsRoverCamera(BaseModel):
    """Mars rover camera data model."""
    
    id: int
    name: str
    rover_id: int
    full_name: str


class MarsRover(BaseModel):
    """Mars rover data model."""
    
    id: int
    name: str
    landing_date: date
    launch_date: date
    status: str
    max_sol: Optional[int] = None
    max_date: Optional[date] = None
    total_photos: Optional[int] = None
    
    @validator('landing_date', 'launch_date', 'max_date', pre=True)
    def parse_date(cls, v):
        if isinstance(v, str):
            return datetime.strptime(v, "%Y-%m-%d").date()
        return v


class MarsRoverPhoto(BaseModel):
    """Mars rover photo data model."""
    
    id: int
    sol: int
    camera: MarsRoverCamera
    img_src: str
    earth_date: date
    rover: MarsRover
    
    @validator('earth_date', pre=True)
    def parse_date(cls, v):
        if isinstance(v, str):
            return datetime.strptime(v, "%Y-%m-%d").date()
        return v


class NEOCloseApproachData(BaseModel):
    """NEO close approach data model."""
    
    close_approach_date: date
    close_approach_date_full: Optional[str] = None
    epoch_date_close_approach: Optional[int] = None
    relative_velocity: Dict[str, str]
    miss_distance: Dict[str, str]
    orbiting_body: str
    
    @validator('close_approach_date', pre=True)
    def parse_date(cls, v):
        if isinstance(v, str):
            return datetime.strptime(v, "%Y-%m-%d").date()
        return v


class NEOEstimatedDiameter(BaseModel):
    """NEO estimated diameter data model."""
    
    kilometers: Dict[str, float]
    meters: Dict[str, float]
    miles: Dict[str, float]
    feet: Dict[str, float]


class NEOObject(BaseModel):
    """Near Earth Object data model."""
    
    id: str
    neo_reference_id: str
    name: str
    nasa_jpl_url: str
    absolute_magnitude_h: float
    estimated_diameter: NEOEstimatedDiameter
    is_potentially_hazardous_asteroid: bool
    close_approach_data: List[NEOCloseApproachData]
    is_sentry_object: Optional[bool] = None
    
    @validator('absolute_magnitude_h', pre=True)
    def parse_magnitude(cls, v):
        return float(v) if v is not None else 0.0


class NEOFeed(BaseModel):
    """NEO feed data model."""
    
    links: Dict[str, Any]
    element_count: int
    near_earth_objects: Dict[str, List[NEOObject]]


class NEOStats(BaseModel):
    """NEO statistics data model."""
    
    near_earth_object_count: int
    close_approach_count: int
    last_updated: str
    source: str
    nasa_jpl_url: str


class APIResponse(BaseModel):
    """Generic API response wrapper."""
    
    success: bool = True
    data: Optional[Any] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    source: str = "NASA API"
    
    class Config:
        arbitrary_types_allowed = True
