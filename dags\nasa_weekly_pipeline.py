"""
NASA API Weekly Pipeline DAG

This DAG runs weekly to perform comprehensive data extraction,
historical data backfill, and analytics updates.
"""

from datetime import datetime, timedelta
import sys
import os

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup

from src.etl.pipeline import ETLPipeline
from src.database.manager import DatabaseManager


# Default arguments for the DAG
default_args = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=10),
    'catchup': False,
}

# Create DAG
dag = DAG(
    'nasa_weekly_pipeline',
    default_args=default_args,
    description='Weekly NASA API comprehensive data pipeline',
    schedule_interval='0 2 * * 0',  # Run weekly on Sunday at 2 AM UTC
    max_active_runs=1,
    tags=['nasa', 'etl', 'weekly', 'comprehensive'],
)


def run_comprehensive_pipeline(**context):
    """Run comprehensive ETL pipeline for the last week."""
    pipeline = ETLPipeline()
    
    # Run full pipeline for last 7 days
    results = pipeline.run_full_pipeline(days_back=7)
    
    # Store results in XCom
    context['task_instance'].xcom_push(key='pipeline_results', value=results)
    
    return f"Processed {results['total_records_processed']} total records"


def backfill_missing_data(**context):
    """Backfill any missing data from the last month."""
    pipeline = ETLPipeline()
    
    # Check for missing APOD data in the last 30 days
    db_manager = DatabaseManager()
    
    missing_dates = []
    for i in range(30):
        check_date = datetime.now().date() - timedelta(days=i)
        apod_record = db_manager.get_apod_by_date(check_date)
        if not apod_record:
            missing_dates.append(check_date.strftime("%Y-%m-%d"))
    
    backfilled_count = 0
    for date_str in missing_dates[:10]:  # Limit to 10 dates to avoid rate limits
        try:
            raw_data = pipeline.apod_extractor.extract_daily_apod(date_str)
            if raw_data:
                transformed_data = pipeline.apod_transformer.transform(raw_data)
                if transformed_data:
                    inserted = db_manager.insert_apod_data(transformed_data)
                    backfilled_count += inserted
        except Exception as e:
            print(f"Failed to backfill data for {date_str}: {e}")
            continue
    
    return f"Backfilled {backfilled_count} APOD records for {len(missing_dates)} missing dates"


def update_rover_manifests(**context):
    """Update all rover manifests with latest information."""
    pipeline = ETLPipeline()
    
    rovers = ['curiosity', 'opportunity', 'spirit']
    updated_rovers = []
    
    for rover in rovers:
        try:
            manifest_data = pipeline.mars_extractor.extract_rover_manifest(rover)
            transformed_manifest = pipeline.mars_transformer.transform_rover_manifest(manifest_data)
            pipeline.db_manager.insert_rover_data(transformed_manifest)
            updated_rovers.append(rover)
        except Exception as e:
            print(f"Failed to update manifest for {rover}: {e}")
            continue
    
    return f"Updated manifests for {len(updated_rovers)} rovers: {', '.join(updated_rovers)}"


def extract_historical_mars_photos(**context):
    """Extract historical Mars rover photos."""
    pipeline = ETLPipeline()
    
    # Extract photos from different sols for variety
    sols_to_check = [1000, 1500, 2000, 2500, 3000]
    total_photos = 0
    
    for sol in sols_to_check:
        try:
            raw_photos = pipeline.mars_extractor.extract_rover_photos(
                rover='curiosity',
                sol=sol,
                camera=None  # Get all cameras
            )
            
            if raw_photos:
                transformed_photos = pipeline.mars_transformer.transform_photos(raw_photos)
                if transformed_photos:
                    inserted = pipeline.db_manager.insert_rover_photos(transformed_photos)
                    total_photos += inserted
                    
        except Exception as e:
            print(f"Failed to extract photos for sol {sol}: {e}")
            continue
    
    return f"Extracted {total_photos} historical Mars rover photos"


def comprehensive_neo_extraction(**context):
    """Comprehensive NEO data extraction for the last month."""
    pipeline = ETLPipeline()
    
    # Extract NEO data for last 30 days in chunks
    total_neos = 0
    chunk_size = 7  # 7-day chunks to avoid API limits
    
    for i in range(0, 30, chunk_size):
        try:
            end_date = datetime.now().date() - timedelta(days=i)
            start_date = end_date - timedelta(days=chunk_size)
            
            raw_data = pipeline.neo_extractor.extract_neo_feed(
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            
            if raw_data:
                transformed_data = pipeline.neo_transformer.transform(raw_data)
                if transformed_data:
                    inserted = pipeline.db_manager.insert_neo_data(transformed_data)
                    total_neos += inserted
                    
        except Exception as e:
            print(f"Failed to extract NEO data for chunk {i}: {e}")
            continue
    
    return f"Extracted {total_neos} NEO records for the last month"


def update_comprehensive_analytics(**context):
    """Update comprehensive analytics and statistics."""
    pipeline = ETLPipeline()
    db_manager = DatabaseManager()
    
    # Update daily summaries for the last 30 days
    for i in range(30):
        target_date = datetime.now().date() - timedelta(days=i)
        db_manager.update_daily_summary(target_date)
    
    # Get comprehensive statistics
    neo_stats = db_manager.get_neo_statistics()
    
    return f"Updated analytics for 30 days. NEO stats: {neo_stats}"


def data_quality_assessment(**context):
    """Perform comprehensive data quality assessment."""
    pipeline = ETLPipeline()
    db_manager = DatabaseManager()
    
    # Get recent data for quality assessment
    recent_apod = db_manager.get_recent_apod(days=7)
    hazardous_neos = db_manager.get_hazardous_neos()
    
    quality_report = {
        'apod_records_last_week': len(recent_apod),
        'hazardous_neos_total': len(hazardous_neos),
        'assessment_date': datetime.now().isoformat()
    }
    
    # Store quality report in XCom
    context['task_instance'].xcom_push(key='quality_report', value=quality_report)
    
    return f"Quality assessment: {len(recent_apod)} APOD records, {len(hazardous_neos)} hazardous NEOs"


def cleanup_old_data(**context):
    """Clean up old temporary data and logs."""
    # This would typically clean up old log files, temporary data, etc.
    # For now, just return a success message
    return "Data cleanup completed"


def generate_weekly_report(**context):
    """Generate comprehensive weekly report."""
    execution_date = context['ds']
    
    # Get results from previous tasks
    pipeline_results = context['task_instance'].xcom_pull(key='pipeline_results')
    quality_report = context['task_instance'].xcom_pull(key='quality_report')
    
    backfill_result = context['task_instance'].xcom_pull(task_ids='backfill_missing_data')
    rover_result = context['task_instance'].xcom_pull(task_ids='update_rover_manifests')
    mars_result = context['task_instance'].xcom_pull(task_ids='extract_historical_mars_photos')
    neo_result = context['task_instance'].xcom_pull(task_ids='comprehensive_neo_extraction')
    analytics_result = context['task_instance'].xcom_pull(task_ids='update_comprehensive_analytics')
    quality_result = context['task_instance'].xcom_pull(task_ids='data_quality_assessment')
    
    report = f"""
    NASA Weekly Pipeline Report - {execution_date}
    =============================================
    
    COMPREHENSIVE PIPELINE RESULTS:
    - Total Records Processed: {pipeline_results.get('total_records_processed', 0) if pipeline_results else 0}
    - Duration: {pipeline_results.get('duration_seconds', 0):.2f} seconds
    
    BACKFILL OPERATIONS:
    - {backfill_result or 'No backfill performed'}
    
    ROVER OPERATIONS:
    - {rover_result or 'No rover updates'}
    - {mars_result or 'No historical photos extracted'}
    
    NEO OPERATIONS:
    - {neo_result or 'No NEO data extracted'}
    
    ANALYTICS:
    - {analytics_result or 'No analytics updated'}
    
    DATA QUALITY:
    - {quality_result or 'No quality assessment performed'}
    - APOD Records (Last Week): {quality_report.get('apod_records_last_week', 0) if quality_report else 0}
    - Hazardous NEOs: {quality_report.get('hazardous_neos_total', 0) if quality_report else 0}
    
    Pipeline completed at {datetime.now()}
    """
    
    print(report)
    return report


# Define tasks
comprehensive_pipeline_task = PythonOperator(
    task_id='run_comprehensive_pipeline',
    python_callable=run_comprehensive_pipeline,
    dag=dag,
)

backfill_task = PythonOperator(
    task_id='backfill_missing_data',
    python_callable=backfill_missing_data,
    dag=dag,
)

rover_manifest_task = PythonOperator(
    task_id='update_rover_manifests',
    python_callable=update_rover_manifests,
    dag=dag,
)

historical_mars_task = PythonOperator(
    task_id='extract_historical_mars_photos',
    python_callable=extract_historical_mars_photos,
    dag=dag,
)

comprehensive_neo_task = PythonOperator(
    task_id='comprehensive_neo_extraction',
    python_callable=comprehensive_neo_extraction,
    dag=dag,
)

analytics_task = PythonOperator(
    task_id='update_comprehensive_analytics',
    python_callable=update_comprehensive_analytics,
    dag=dag,
)

quality_task = PythonOperator(
    task_id='data_quality_assessment',
    python_callable=data_quality_assessment,
    dag=dag,
)

cleanup_task = PythonOperator(
    task_id='cleanup_old_data',
    python_callable=cleanup_old_data,
    dag=dag,
)

report_task = PythonOperator(
    task_id='generate_weekly_report',
    python_callable=generate_weekly_report,
    dag=dag,
)

# Define task dependencies
comprehensive_pipeline_task >> [backfill_task, rover_manifest_task, comprehensive_neo_task]
rover_manifest_task >> historical_mars_task
[backfill_task, historical_mars_task, comprehensive_neo_task] >> analytics_task
analytics_task >> quality_task >> cleanup_task >> report_task
