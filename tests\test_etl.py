"""Tests for ETL pipeline components."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import date, datetime
from decimal import Decimal

from src.etl.extractors import APODExtractor, MarsRoverExtractor, NEOExtractor
from src.etl.transformers import APODTransformer, MarsRoverTransformer, NEOTransformer, DataQualityChecker
from src.etl.pipeline import ETLPipeline


class TestAPODExtractor:
    """Test APOD data extractor."""
    
    def setup_method(self):
        """Set up test extractor."""
        self.extractor = APODExtractor()
    
    @patch.object(APODExtractor, 'extract_with_retry')
    def test_extract_daily_apod(self, mock_extract):
        """Test extracting daily APOD data."""
        mock_apod = Mock()
        mock_apod.date = date(2024, 1, 1)
        mock_apod.title = "Test APOD"
        mock_apod.explanation = "Test explanation"
        mock_apod.url = "https://example.com/image.jpg"
        mock_apod.hdurl = None
        mock_apod.media_type = "image"
        mock_apod.copyright = None
        
        mock_extract.return_value = [mock_apod]
        
        result = self.extractor.extract_daily_apod("2024-01-01")
        
        assert len(result) == 1
        assert result[0]['title'] == "Test APOD"
        assert result[0]['date'] == date(2024, 1, 1)
    
    @patch.object(APODExtractor, 'extract_with_retry')
    def test_extract_apod_range(self, mock_extract):
        """Test extracting APOD range."""
        mock_apods = []
        for i in range(3):
            mock_apod = Mock()
            mock_apod.date = date(2024, 1, i+1)
            mock_apod.title = f"Test APOD {i+1}"
            mock_apod.explanation = f"Test explanation {i+1}"
            mock_apod.url = f"https://example.com/image{i+1}.jpg"
            mock_apod.hdurl = None
            mock_apod.media_type = "image"
            mock_apod.copyright = None
            mock_apods.append(mock_apod)
        
        mock_extract.return_value = mock_apods
        
        result = self.extractor.extract_apod_range(days_back=3)
        
        assert len(result) == 3
        assert result[0]['title'] == "Test APOD 1"
        assert result[2]['title'] == "Test APOD 3"


class TestMarsRoverExtractor:
    """Test Mars rover data extractor."""
    
    def setup_method(self):
        """Set up test extractor."""
        self.extractor = MarsRoverExtractor()
    
    @patch.object(MarsRoverExtractor, 'extract_with_retry')
    def test_extract_rover_photos(self, mock_extract):
        """Test extracting rover photos."""
        mock_photo = Mock()
        mock_photo.id = 123
        mock_photo.sol = 1000
        mock_photo.earth_date = date(2024, 1, 1)
        mock_photo.img_src = "https://example.com/photo.jpg"
        mock_photo.rover.id = 5
        mock_photo.camera.id = 1
        
        mock_extract.return_value = [mock_photo]
        
        result = self.extractor.extract_rover_photos(rover="curiosity", sol=1000)
        
        assert len(result) == 1
        assert result[0]['photo_id'] == 123
        assert result[0]['sol'] == 1000
    
    @patch.object(MarsRoverExtractor, 'extract_with_retry')
    def test_extract_rover_manifest(self, mock_extract):
        """Test extracting rover manifest."""
        mock_manifest = {
            'photo_manifest': {
                'id': 5,
                'name': 'Curiosity',
                'landing_date': '2012-08-05',
                'launch_date': '2011-11-26',
                'status': 'active',
                'max_sol': 3000,
                'max_date': '2024-01-01',
                'total_photos': 500000
            }
        }
        
        mock_extract.return_value = mock_manifest
        
        result = self.extractor.extract_rover_manifest("curiosity")
        
        assert result['name'] == 'Curiosity'
        assert result['status'] == 'active'
        assert result['total_photos'] == 500000


class TestNEOExtractor:
    """Test NEO data extractor."""
    
    def setup_method(self):
        """Set up test extractor."""
        self.extractor = NEOExtractor()
    
    @patch.object(NEOExtractor, 'extract_with_retry')
    def test_extract_neo_feed(self, mock_extract):
        """Test extracting NEO feed."""
        mock_neo = Mock()
        mock_neo.id = "123"
        mock_neo.neo_reference_id = "123"
        mock_neo.name = "Test NEO"
        mock_neo.nasa_jpl_url = "https://example.com"
        mock_neo.absolute_magnitude_h = 20.5
        mock_neo.estimated_diameter.kilometers = {
            'estimated_diameter_min': 0.1,
            'estimated_diameter_max': 0.2
        }
        mock_neo.is_potentially_hazardous_asteroid = False
        mock_neo.close_approach_data = []
        
        mock_extract.return_value = [mock_neo]
        
        result = self.extractor.extract_neo_feed("2024-01-01", "2024-01-01")
        
        assert len(result) == 1
        assert result[0]['neo_id'] == "123"
        assert result[0]['name'] == "Test NEO"


class TestAPODTransformer:
    """Test APOD data transformer."""
    
    def setup_method(self):
        """Set up test transformer."""
        self.transformer = APODTransformer()
    
    def test_transform_valid_data(self):
        """Test transforming valid APOD data."""
        raw_data = [{
            'date': date(2024, 1, 1),
            'title': 'Test APOD',
            'explanation': 'Test explanation',
            'url': 'https://example.com/image.jpg',
            'media_type': 'image',
            'copyright': 'NASA'
        }]
        
        result = self.transformer.transform(raw_data)
        
        assert len(result) == 1
        assert result[0]['title'] == 'Test APOD'
        assert result[0]['media_type'] == 'image'
    
    def test_transform_long_title(self):
        """Test transforming data with long title."""
        long_title = 'A' * 600  # Longer than 500 character limit
        
        raw_data = [{
            'date': date(2024, 1, 1),
            'title': long_title,
            'explanation': 'Test explanation',
            'url': 'https://example.com/image.jpg',
            'media_type': 'image'
        }]
        
        result = self.transformer.transform(raw_data)
        
        assert len(result) == 1
        assert len(result[0]['title']) == 500  # Truncated to 500 chars
        assert result[0]['title'].endswith('...')
    
    def test_transform_invalid_url(self):
        """Test transforming data with invalid URL."""
        raw_data = [{
            'date': date(2024, 1, 1),
            'title': 'Test APOD',
            'explanation': 'Test explanation',
            'url': 'invalid-url',
            'media_type': 'image'
        }]
        
        result = self.transformer.transform(raw_data)
        
        assert len(result) == 1
        assert result[0]['url'] is None  # Invalid URL should be None
    
    def test_calculate_quality_score(self):
        """Test calculating quality score."""
        data = [
            {
                'title': 'Test APOD',
                'explanation': 'Test explanation',
                'url': 'https://example.com/image.jpg',
                'date': date(2024, 1, 1),
                'hdurl': 'https://example.com/hd.jpg',
                'copyright': 'NASA'
            }
        ]
        
        score = self.transformer.calculate_quality_score(data)
        
        assert score == 110.0  # 100 for required fields + 10 for optional fields


class TestNEOTransformer:
    """Test NEO data transformer."""
    
    def setup_method(self):
        """Set up test transformer."""
        self.transformer = NEOTransformer()
    
    def test_transform_valid_data(self):
        """Test transforming valid NEO data."""
        raw_data = [{
            'neo_id': '123',
            'neo_reference_id': '123',
            'name': 'Test NEO',
            'nasa_jpl_url': 'https://example.com',
            'absolute_magnitude_h': 20.5,
            'estimated_diameter_min_km': 0.1,
            'estimated_diameter_max_km': 0.2,
            'is_potentially_hazardous': False,
            'close_approach_data': []
        }]
        
        result = self.transformer.transform(raw_data)
        
        assert len(result) == 1
        assert result[0]['neo_id'] == '123'
        assert result[0]['name'] == 'Test NEO'
        assert isinstance(result[0]['absolute_magnitude_h'], Decimal)
    
    def test_safe_decimal_conversion(self):
        """Test safe decimal conversion."""
        assert self.transformer._safe_decimal(20.5) == Decimal('20.5')
        assert self.transformer._safe_decimal('20.5') == Decimal('20.5')
        assert self.transformer._safe_decimal(None) is None
        assert self.transformer._safe_decimal('invalid') is None
    
    def test_calculate_hazard_score(self):
        """Test calculating hazard score."""
        neo_data = {
            'is_potentially_hazardous': True,
            'estimated_diameter_max_km': 1.5,  # > 1km
            'close_approach_data': [
                {'miss_distance_km': 500000}  # < 1M km
            ]
        }
        
        score = self.transformer.calculate_hazard_score(neo_data)
        
        assert score == 100.0  # 50 (hazardous) + 30 (size) + 20 (distance)


class TestDataQualityChecker:
    """Test data quality checker."""
    
    def setup_method(self):
        """Set up test quality checker."""
        self.checker = DataQualityChecker()
    
    def test_check_data_completeness_full(self):
        """Test completeness check with complete data."""
        data = [
            {'field1': 'value1', 'field2': 'value2'},
            {'field1': 'value3', 'field2': 'value4'}
        ]
        required_fields = ['field1', 'field2']
        
        result = self.checker.check_data_completeness(data, required_fields)
        
        assert result['completeness_score'] == 100.0
        assert len(result['missing_fields']) == 0
    
    def test_check_data_completeness_partial(self):
        """Test completeness check with partial data."""
        data = [
            {'field1': 'value1', 'field2': 'value2'},
            {'field1': 'value3', 'field2': None}  # Missing field2
        ]
        required_fields = ['field1', 'field2']
        
        result = self.checker.check_data_completeness(data, required_fields)
        
        assert result['completeness_score'] == 75.0  # field1: 100%, field2: 50%
        assert 'field2' in result['missing_fields']
    
    def test_check_data_validity(self):
        """Test data validity check."""
        data = [
            {'id': 1, 'name': 'Valid Record', 'date': '2024-01-01'},
            {'id': 2, 'name': '', 'date': '2024-01-02'},  # Empty name
            {'id': None, 'name': 'Invalid ID', 'date': '2024-01-03'}  # Null ID
        ]
        
        result = self.checker.check_data_validity(data)
        
        assert result['validity_score'] < 100.0
        assert result['invalid_records'] == 2
        assert len(result['issues']) > 0


class TestETLPipeline:
    """Test ETL pipeline orchestrator."""
    
    def setup_method(self):
        """Set up test pipeline."""
        with patch('src.etl.pipeline.DatabaseManager'), \
             patch('src.etl.pipeline.APODExtractor'), \
             patch('src.etl.pipeline.MarsRoverExtractor'), \
             patch('src.etl.pipeline.NEOExtractor'):
            self.pipeline = ETLPipeline()
    
    @patch.object(ETLPipeline, 'run_apod_pipeline')
    @patch.object(ETLPipeline, 'run_mars_pipeline')
    @patch.object(ETLPipeline, 'run_neo_pipeline')
    @patch.object(ETLPipeline, 'update_analytics')
    def test_run_full_pipeline(self, mock_analytics, mock_neo, mock_mars, mock_apod):
        """Test running full pipeline."""
        # Mock pipeline results
        mock_apod.return_value = {'records_inserted': 5}
        mock_mars.return_value = {'records_inserted': 10}
        mock_neo.return_value = {'records_inserted': 15}
        
        result = self.pipeline.run_full_pipeline(days_back=7)
        
        assert result['total_records_processed'] == 30
        assert 'start_time' in result
        assert 'end_time' in result
        assert 'duration_seconds' in result
        
        mock_apod.assert_called_once_with(7)
        mock_mars.assert_called_once()
        mock_neo.assert_called_once_with(7)
        mock_analytics.assert_called_once()
    
    def test_health_check(self):
        """Test pipeline health check."""
        # Mock health check methods
        self.pipeline.db_manager.health_check = Mock(return_value=True)
        self.pipeline.apod_extractor.client.health_check = Mock(return_value=True)
        
        result = self.pipeline.health_check()
        
        assert result['database'] is True
        assert result['nasa_api'] is True
        assert result['overall'] is True


@pytest.fixture
def sample_apod_data():
    """Sample APOD data for testing."""
    return [{
        'date': date(2024, 1, 1),
        'title': 'Test APOD',
        'explanation': 'Test explanation',
        'url': 'https://example.com/image.jpg',
        'media_type': 'image'
    }]


@pytest.fixture
def sample_neo_data():
    """Sample NEO data for testing."""
    return [{
        'neo_id': '123',
        'neo_reference_id': '123',
        'name': 'Test NEO',
        'nasa_jpl_url': 'https://example.com',
        'absolute_magnitude_h': 20.5,
        'estimated_diameter_min_km': 0.1,
        'estimated_diameter_max_km': 0.2,
        'is_potentially_hazardous': False,
        'close_approach_data': []
    }]


class TestETLIntegration:
    """Integration tests for ETL components."""
    
    def test_apod_extract_transform_flow(self, sample_apod_data):
        """Test APOD extract-transform flow."""
        transformer = APODTransformer()
        
        # Transform the sample data
        result = transformer.transform(sample_apod_data)
        
        assert len(result) == 1
        assert result[0]['title'] == 'Test APOD'
        assert result[0]['media_type'] == 'image'
    
    def test_neo_extract_transform_flow(self, sample_neo_data):
        """Test NEO extract-transform flow."""
        transformer = NEOTransformer()
        
        # Transform the sample data
        result = transformer.transform(sample_neo_data)
        
        assert len(result) == 1
        assert result[0]['neo_id'] == '123'
        assert result[0]['name'] == 'Test NEO'
