# NASA API Data Pipeline

A comprehensive data engineering portfolio project that demonstrates building a production-ready data pipeline using NASA's public APIs. This project showcases modern data engineering practices with Apache Airflow, PostgreSQL, Streamlit, and Docker.

## 🚀 Features

- **Multi-source Data Extraction**: Automated data collection from multiple NASA APIs
  - Astronomy Picture of the Day (APOD)
  - Mars Rover Photos
  - Near Earth Objects (NEO)
- **Robust ETL Pipeline**: Apache Airflow orchestrated data processing
- **Data Storage**: PostgreSQL database with optimized schemas
- **Interactive Dashboard**: Streamlit-based visualization and analytics
- **Containerized Deployment**: Docker Compose for easy deployment
- **Memory Optimized**: Configured for Windows 10 with 6GB RAM
- **Modern Python**: Uses UV package manager and Python 3.11+

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   NASA APIs     │    │   Apache        │    │   PostgreSQL    │
│                 │───▶│   Airflow       │───▶│   Database      │
│ • APOD          │    │                 │    │                 │
│ • Mars Rovers   │    │ • Scheduling    │    │ • Raw Data      │
│ • NEO           │    │ • Monitoring    │    │ • Processed     │
└─────────────────┘    │ • Erro<PERSON> Handling│    │ • Analytics     │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Streamlit     │
                       │   Dashboard     │
                       │                 │
                       │ • Visualizations│
                       │ • Analytics     │
                       │ • Monitoring    │
                       └─────────────────┘
```

## 📋 Prerequisites

- Windows 10 with 6GB+ RAM
- Docker Desktop installed and running
- Python 3.11+
- UV package manager
- NASA API Key (optional, DEMO_KEY works with limitations)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd NASA_API_Pipeline
   ```

2. **Install UV package manager** (if not already installed):
   ```bash
   pip install uv
   ```

3. **Install dependencies**:
   ```bash
   uv sync
   ```

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env file with your NASA API key and other configurations
   ```

5. **Get NASA API Key** (recommended):
   - Visit [NASA API Portal](https://api.nasa.gov/)
   - Sign up for a free API key
   - Add it to your `.env` file

## 🚀 Quick Start

### Option 1: Docker Compose (Recommended)

1. **Start all services**:
   ```bash
   docker-compose up -d
   ```

2. **Access the applications**:
   - Airflow Web UI: http://localhost:8080 (admin/admin)
   - Streamlit Dashboard: http://localhost:8501
   - PostgreSQL: localhost:5432

3. **Run the pipeline**:
   ```bash
   uv run main.py pipeline
   ```

### Option 2: Local Development

1. **Start PostgreSQL** (using Docker):
   ```bash
   docker-compose up postgres -d
   ```

2. **Initialize database**:
   ```bash
   uv run main.py setup
   ```

3. **Run pipeline components**:
   ```bash
   # Extract data
   uv run main.py extract

   # Transform data
   uv run main.py transform

   # Load data
   uv run main.py load

   # Run complete pipeline
   uv run main.py pipeline

   # Launch dashboard
   uv run main.py dashboard
   ```

## 📊 Data Sources

### 1. Astronomy Picture of the Day (APOD)
- Daily astronomy images and explanations
- Historical data available from 1995
- Includes metadata: title, explanation, copyright, media type

### 2. Mars Rover Photos
- Images from Curiosity, Opportunity, and Spirit rovers
- Multiple camera perspectives (FHAZ, RHAZ, MAST, CHEMCAM, etc.)
- Sol (Martian day) and Earth date information

### 3. Near Earth Objects (NEO)
- Asteroid and comet data
- Close approach information
- Potentially hazardous object identification
- Orbital characteristics and size estimates

## 🗄️ Database Schema

The pipeline uses PostgreSQL with three main schemas:

- **raw_data**: Direct API responses
- **processed_data**: Cleaned and transformed data
- **analytics**: Aggregated data for reporting

Key tables:
- `raw_data.apod`: Astronomy pictures
- `raw_data.mars_rover_photos`: Mars rover images
- `raw_data.neo_objects`: Near Earth objects
- `processed_data.daily_summary`: Daily aggregations
- `analytics.popular_cameras`: Camera usage statistics

## 📈 Dashboard Features

The Streamlit dashboard provides:

- **Overview**: Key metrics and recent data
- **APOD Gallery**: Browse astronomy pictures
- **Mars Exploration**: Rover photos and statistics
- **NEO Tracker**: Near Earth object monitoring
- **Pipeline Status**: ETL job monitoring
- **Data Quality**: Validation and error reports

## ⚙️ Configuration

Key configuration options in `.env`:

```env
# NASA API
NASA_API_KEY=your_api_key_here

# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=nasa_pipeline
POSTGRES_USER=airflow
POSTGRES_PASSWORD=airflow

# Airflow
AIRFLOW_HOME=./airflow
AIRFLOW__CORE__EXECUTOR=LocalExecutor

# Streamlit
STREAMLIT_PORT=8501
```

## 🔧 Development

### Project Structure
```
NASA_API_Pipeline/
├── src/
│   ├── api/           # NASA API clients
│   ├── config/        # Configuration management
│   ├── database/      # Database operations
│   ├── etl/          # ETL processes
│   └── dashboard/     # Streamlit app
├── dags/             # Airflow DAGs
├── sql/              # Database schemas
├── tests/            # Unit tests
├── docker-compose.yml
├── Dockerfile
├── main.py           # Entry point
└── pyproject.toml    # Dependencies
```

### Running Tests
```bash
uv run pytest tests/
```

### Code Quality
```bash
uv run black src/
uv run flake8 src/
uv run mypy src/
```

## 📝 Usage Examples

### Manual Data Extraction
```python
from src.api.nasa_client import NASAClient

client = NASAClient()
apod_data = client.get_apod(date="2024-01-01")
mars_photos = client.get_mars_rover_photos(rover="curiosity", sol=1000)
neo_data = client.get_neo_feed(start_date="2024-01-01", end_date="2024-01-07")
```

### Database Operations
```python
from src.database.manager import DatabaseManager

db = DatabaseManager()
db.insert_apod_data(apod_data)
db.insert_mars_photos(mars_photos)
db.insert_neo_data(neo_data)
```

## 🚨 Troubleshooting

### Common Issues

1. **Memory Issues**: Reduce Docker memory limits in docker-compose.yml
2. **API Rate Limits**: Get a NASA API key for higher limits
3. **Port Conflicts**: Change ports in docker-compose.yml if needed
4. **Database Connection**: Ensure PostgreSQL is running and accessible

### Logs
- Airflow logs: `./logs/`
- Application logs: Check Docker container logs
- Database logs: PostgreSQL container logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- NASA for providing free public APIs
- Apache Airflow community
- Streamlit team
- PostgreSQL developers

## 📞 Support

For questions or issues:
1. Check the troubleshooting section
2. Review existing GitHub issues
3. Create a new issue with detailed information

---

**Built with ❤️ for the data engineering community**