"""Configuration settings for NASA API Pipeline."""

import os
from pathlib import Path
from typing import Optional

from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings."""
    
    # NASA API Configuration
    nasa_api_key: str = Field(default="DEMO_KEY", env="NASA_API_KEY")
    nasa_api_base_url: str = "https://api.nasa.gov"
    
    # Database Configuration
    postgres_host: str = Field(default="localhost", env="POSTGRES_HOST")
    postgres_port: int = Field(default=5432, env="POSTGRES_PORT")
    postgres_db: str = Field(default="nasa_pipeline", env="POSTGRES_DB")
    postgres_user: str = Field(default="airflow", env="POSTGRES_USER")
    postgres_password: str = Field(default="airflow", env="POSTGRES_PASSWORD")
    
    # Airflow Configuration
    airflow_home: str = Field(default="./airflow", env="AIRFLOW_HOME")
    airflow_dags_folder: str = Field(default="./dags", env="AIRFLOW__CORE__DAGS_FOLDER")
    
    # Streamlit Configuration
    streamlit_port: int = Field(default=8501, env="STREAMLIT_PORT")
    
    # Data Storage
    data_dir: Path = Field(default=Path("./data"))
    logs_dir: Path = Field(default=Path("./logs"))
    
    # API Rate Limiting
    api_rate_limit: int = Field(default=1000, description="Requests per hour")
    api_retry_attempts: int = Field(default=3)
    api_retry_delay: int = Field(default=1)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @property
    def database_url(self) -> str:
        """Get database connection URL."""
        return (
            f"postgresql://{self.postgres_user}:{self.postgres_password}"
            f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
        )
    
    @property
    def airflow_database_url(self) -> str:
        """Get Airflow database connection URL."""
        return self.database_url.replace(self.postgres_db, "airflow")


# Global settings instance
settings = Settings()
