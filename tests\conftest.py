"""Pytest configuration and shared fixtures."""

import pytest
import os
import sys
from unittest.mock import Mock, patch
from datetime import date, datetime

# Add src to Python path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@pytest.fixture(scope="session")
def test_settings():
    """Test settings configuration."""
    return {
        'nasa_api_key': 'TEST_API_KEY',
        'nasa_api_base_url': 'https://api.nasa.gov',
        'postgres_host': 'localhost',
        'postgres_port': 5432,
        'postgres_db': 'test_nasa_pipeline',
        'postgres_user': 'test_user',
        'postgres_password': 'test_password',
        'api_rate_limit': 1000,
        'api_retry_attempts': 3,
        'api_retry_delay': 1
    }


@pytest.fixture
def mock_nasa_api_response():
    """Mock NASA API response data."""
    return {
        'apod': {
            "date": "2024-01-01",
            "title": "Test APOD",
            "explanation": "Test explanation for APOD",
            "url": "https://example.com/image.jpg",
            "hdurl": "https://example.com/hd_image.jpg",
            "media_type": "image",
            "copyright": "NASA",
            "service_version": "v1"
        },
        'mars_photos': {
            "photos": [
                {
                    "id": 123456,
                    "sol": 1000,
                    "camera": {
                        "id": 1,
                        "name": "FHAZ",
                        "rover_id": 5,
                        "full_name": "Front Hazard Avoidance Camera"
                    },
                    "img_src": "https://example.com/mars_photo.jpg",
                    "earth_date": "2024-01-01",
                    "rover": {
                        "id": 5,
                        "name": "curiosity",
                        "landing_date": "2012-08-05",
                        "launch_date": "2011-11-26",
                        "status": "active",
                        "max_sol": 3000,
                        "max_date": "2024-01-01",
                        "total_photos": 500000
                    }
                }
            ]
        },
        'neo_feed': {
            "near_earth_objects": {
                "2024-01-01": [
                    {
                        "id": "123456",
                        "neo_reference_id": "123456",
                        "name": "Test NEO (2024 AA)",
                        "nasa_jpl_url": "https://ssd.jpl.nasa.gov/tools/sbdb_lookup.html#/?sstr=123456",
                        "absolute_magnitude_h": 20.5,
                        "estimated_diameter": {
                            "kilometers": {
                                "estimated_diameter_min": 0.1,
                                "estimated_diameter_max": 0.2
                            },
                            "meters": {
                                "estimated_diameter_min": 100,
                                "estimated_diameter_max": 200
                            }
                        },
                        "is_potentially_hazardous_asteroid": False,
                        "close_approach_data": [
                            {
                                "close_approach_date": "2024-01-01",
                                "close_approach_date_full": "2024-Jan-01 12:00",
                                "epoch_date_close_approach": 1704110400000,
                                "relative_velocity": {
                                    "kilometers_per_second": "15.5",
                                    "kilometers_per_hour": "55800"
                                },
                                "miss_distance": {
                                    "astronomical": "0.1",
                                    "lunar": "38.9",
                                    "kilometers": "15000000",
                                    "miles": "9320000"
                                },
                                "orbiting_body": "Earth"
                            }
                        ],
                        "is_sentry_object": False
                    }
                ]
            }
        }
    }


@pytest.fixture
def mock_database_session():
    """Mock database session for testing."""
    session = Mock()
    session.query.return_value = session
    session.filter.return_value = session
    session.filter_by.return_value = session
    session.first.return_value = None
    session.all.return_value = []
    session.count.return_value = 0
    session.add.return_value = None
    session.commit.return_value = None
    session.rollback.return_value = None
    session.close.return_value = None
    return session


@pytest.fixture
def sample_apod_records():
    """Sample APOD records for testing."""
    return [
        {
            'date': date(2024, 1, 1),
            'title': 'Test APOD 1',
            'explanation': 'Test explanation 1',
            'url': 'https://example.com/image1.jpg',
            'hdurl': 'https://example.com/hd1.jpg',
            'media_type': 'image',
            'copyright': 'NASA'
        },
        {
            'date': date(2024, 1, 2),
            'title': 'Test APOD 2',
            'explanation': 'Test explanation 2',
            'url': 'https://example.com/image2.jpg',
            'hdurl': None,
            'media_type': 'video',
            'copyright': None
        }
    ]


@pytest.fixture
def sample_neo_records():
    """Sample NEO records for testing."""
    return [
        {
            'neo_id': '123',
            'neo_reference_id': '123',
            'name': 'Test NEO 1',
            'nasa_jpl_url': 'https://example.com/neo1',
            'absolute_magnitude_h': 20.5,
            'estimated_diameter_min_km': 0.1,
            'estimated_diameter_max_km': 0.2,
            'is_potentially_hazardous': False,
            'close_approach_data': []
        },
        {
            'neo_id': '456',
            'neo_reference_id': '456',
            'name': 'Test NEO 2 (Hazardous)',
            'nasa_jpl_url': 'https://example.com/neo2',
            'absolute_magnitude_h': 18.0,
            'estimated_diameter_min_km': 0.5,
            'estimated_diameter_max_km': 1.2,
            'is_potentially_hazardous': True,
            'close_approach_data': [
                {
                    'close_approach_date': date(2024, 6, 15),
                    'relative_velocity_kmh': 55800.0,
                    'miss_distance_km': 15000000.0,
                    'orbiting_body': 'Earth'
                }
            ]
        }
    ]


@pytest.fixture
def sample_mars_photos():
    """Sample Mars rover photos for testing."""
    return [
        {
            'photo_id': 123456,
            'sol': 1000,
            'earth_date': date(2024, 1, 1),
            'img_src': 'https://example.com/mars1.jpg',
            'rover_id': 5,
            'camera_id': 1
        },
        {
            'photo_id': 123457,
            'sol': 1001,
            'earth_date': date(2024, 1, 2),
            'img_src': 'https://example.com/mars2.jpg',
            'rover_id': 5,
            'camera_id': 2
        }
    ]


@pytest.fixture(autouse=True)
def mock_settings():
    """Mock settings for all tests."""
    with patch('src.config.settings.settings') as mock:
        mock.nasa_api_key = 'TEST_API_KEY'
        mock.nasa_api_base_url = 'https://api.nasa.gov'
        mock.postgres_host = 'localhost'
        mock.postgres_port = 5432
        mock.postgres_db = 'test_nasa_pipeline'
        mock.postgres_user = 'test_user'
        mock.postgres_password = 'test_password'
        mock.database_url = 'postgresql://test_user:test_password@localhost:5432/test_nasa_pipeline'
        mock.api_rate_limit = 1000
        mock.api_retry_attempts = 3
        mock.api_retry_delay = 1
        yield mock


@pytest.fixture
def mock_requests_get():
    """Mock requests.get for API testing."""
    with patch('requests.get') as mock_get:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"test": "data"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        yield mock_get


@pytest.fixture
def mock_database_manager():
    """Mock database manager for testing."""
    with patch('src.database.manager.DatabaseManager') as mock_manager:
        manager_instance = Mock()
        manager_instance.health_check.return_value = True
        manager_instance.insert_apod_data.return_value = 1
        manager_instance.insert_neo_data.return_value = 1
        manager_instance.insert_rover_photos.return_value = 1
        manager_instance.get_recent_apod.return_value = []
        manager_instance.get_neo_statistics.return_value = {
            'total_neo_count': 100,
            'hazardous_neo_count': 25,
            'hazardous_percentage': 25.0
        }
        mock_manager.return_value = manager_instance
        yield manager_instance


# Pytest markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test (requires external services)"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "api: mark test as requiring API access"
    )


# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Mark integration tests
        if "integration" in item.nodeid.lower():
            item.add_marker(pytest.mark.integration)
        
        # Mark API tests
        if "api" in item.nodeid.lower() or "test_nasa_client" in item.nodeid:
            item.add_marker(pytest.mark.api)
        
        # Mark slow tests
        if "test_full_pipeline" in item.nodeid or "comprehensive" in item.nodeid.lower():
            item.add_marker(pytest.mark.slow)
