# NASA API Configuration
NASA_API_KEY=your_nasa_api_key_here

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=nasa_pipeline
POSTGRES_USER=airflow
POSTGRES_PASSWORD=airflow

# Airflow Configuration
AIRFLOW_HOME=./airflow
AIRFLOW__CORE__DAGS_FOLDER=./dags
AIRFLOW__CORE__EXECUTOR=LocalExecutor
AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql://airflow:airflow@localhost:5432/airflow
AIRFLOW__CORE__FERNET_KEY=your_fernet_key_here
AIRFLOW__WEBSERVER__SECRET_KEY=your_secret_key_here

# Streamlit Configuration
STREAMLIT_PORT=8501

# Docker Configuration
COMPOSE_PROJECT_NAME=nasa-pipeline
