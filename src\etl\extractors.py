"""Data extractors for NASA API pipeline."""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import time

from src.api.nasa_client import NASAClient
from src.api.utils import parse_apod_response, parse_mars_rover_response, parse_neo_response
from src.config.settings import settings


class DataExtractor:
    """Base data extractor class."""
    
    def __init__(self):
        self.client = NASAClient()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def extract_with_retry(self, extract_func, *args, **kwargs) -> Optional[Any]:
        """Execute extraction with retry logic."""
        for attempt in range(settings.api_retry_attempts):
            try:
                return extract_func(*args, **kwargs)
            except Exception as e:
                self.logger.warning(f"Extraction attempt {attempt + 1} failed: {e}")
                if attempt < settings.api_retry_attempts - 1:
                    time.sleep(settings.api_retry_delay * (attempt + 1))
                else:
                    self.logger.error(f"All extraction attempts failed: {e}")
                    raise
        return None


class APODExtractor(DataExtractor):
    """APOD data extractor."""
    
    def extract_daily_apod(self, target_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Extract APOD data for a specific date."""
        if target_date is None:
            target_date = date.today().strftime("%Y-%m-%d")
        
        self.logger.info(f"Extracting APOD data for {target_date}")
        
        def _extract():
            response = self.client.get_apod(date=target_date)
            return parse_apod_response(response)
        
        parsed_data = self.extract_with_retry(_extract)
        
        # Convert to dictionary format for database insertion
        result = []
        for apod in parsed_data:
            result.append({
                'date': apod.date,
                'title': apod.title,
                'explanation': apod.explanation,
                'url': apod.url,
                'hdurl': apod.hdurl,
                'media_type': apod.media_type,
                'copyright': apod.copyright,
                'service_version': getattr(apod, 'service_version', None)
            })
        
        self.logger.info(f"Extracted {len(result)} APOD records")
        return result
    
    def extract_apod_range(self, days_back: int = 7) -> List[Dict[str, Any]]:
        """Extract APOD data for a date range."""
        end_date = date.today()
        start_date = end_date - timedelta(days=days_back)
        
        self.logger.info(f"Extracting APOD data from {start_date} to {end_date}")
        
        def _extract():
            response = self.client.get_apod(
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d")
            )
            return parse_apod_response(response)
        
        parsed_data = self.extract_with_retry(_extract)
        
        result = []
        for apod in parsed_data:
            result.append({
                'date': apod.date,
                'title': apod.title,
                'explanation': apod.explanation,
                'url': apod.url,
                'hdurl': apod.hdurl,
                'media_type': apod.media_type,
                'copyright': apod.copyright,
                'service_version': getattr(apod, 'service_version', None)
            })
        
        self.logger.info(f"Extracted {len(result)} APOD records")
        return result


class MarsRoverExtractor(DataExtractor):
    """Mars rover data extractor."""
    
    def extract_rover_photos(
        self, 
        rover: str = "curiosity", 
        sol: Optional[int] = None,
        earth_date: Optional[str] = None,
        camera: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Extract Mars rover photos."""
        self.logger.info(f"Extracting {rover} photos for sol={sol}, earth_date={earth_date}")
        
        def _extract():
            response = self.client.get_mars_rover_photos(
                rover=rover,
                sol=sol,
                earth_date=earth_date,
                camera=camera
            )
            return parse_mars_rover_response(response)
        
        parsed_data = self.extract_with_retry(_extract)
        
        result = []
        for photo in parsed_data:
            result.append({
                'photo_id': photo.id,
                'sol': photo.sol,
                'earth_date': photo.earth_date,
                'img_src': photo.img_src,
                'rover_id': photo.rover.id,
                'camera_id': photo.camera.id
            })
        
        self.logger.info(f"Extracted {len(result)} rover photos")
        return result
    
    def extract_latest_rover_photos(self, rover: str = "curiosity", limit: int = 25) -> List[Dict[str, Any]]:
        """Extract latest rover photos."""
        self.logger.info(f"Extracting latest {limit} photos from {rover}")
        
        def _extract():
            return self.client.get_latest_rover_photos(rover=rover, limit=limit)
        
        photos = self.extract_with_retry(_extract)
        
        result = []
        for photo in photos:
            result.append({
                'photo_id': photo['id'],
                'sol': photo['sol'],
                'earth_date': datetime.strptime(photo['earth_date'], "%Y-%m-%d").date(),
                'img_src': photo['img_src'],
                'rover_id': photo['rover']['id'],
                'camera_id': photo['camera']['id']
            })
        
        self.logger.info(f"Extracted {len(result)} latest rover photos")
        return result
    
    def extract_rover_manifest(self, rover: str = "curiosity") -> Dict[str, Any]:
        """Extract rover mission manifest."""
        self.logger.info(f"Extracting {rover} manifest")
        
        def _extract():
            return self.client.get_rover_manifest(rover)
        
        manifest_data = self.extract_with_retry(_extract)
        manifest = manifest_data['photo_manifest']
        
        result = {
            'id': manifest['id'],
            'name': manifest['name'],
            'landing_date': datetime.strptime(manifest['landing_date'], "%Y-%m-%d").date(),
            'launch_date': datetime.strptime(manifest['launch_date'], "%Y-%m-%d").date(),
            'status': manifest['status'],
            'max_sol': manifest['max_sol'],
            'max_date': datetime.strptime(manifest['max_date'], "%Y-%m-%d").date(),
            'total_photos': manifest['total_photos']
        }
        
        self.logger.info(f"Extracted manifest for {rover}")
        return result


class NEOExtractor(DataExtractor):
    """NEO data extractor."""
    
    def extract_neo_feed(
        self, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Extract NEO feed data."""
        if start_date is None:
            end_date_obj = date.today()
            start_date_obj = end_date_obj - timedelta(days=7)
            start_date = start_date_obj.strftime("%Y-%m-%d")
            end_date = end_date_obj.strftime("%Y-%m-%d")
        
        self.logger.info(f"Extracting NEO feed from {start_date} to {end_date}")
        
        def _extract():
            response = self.client.get_neo_feed(start_date=start_date, end_date=end_date)
            return parse_neo_response(response)
        
        parsed_data = self.extract_with_retry(_extract)
        
        result = []
        for neo in parsed_data:
            # Extract close approach data
            close_approaches = []
            for approach in neo.close_approach_data:
                close_approaches.append({
                    'close_approach_date': approach.close_approach_date,
                    'close_approach_date_full': approach.close_approach_date_full,
                    'epoch_date_close_approach': approach.epoch_date_close_approach,
                    'relative_velocity_kmh': float(approach.relative_velocity.get('kilometers_per_hour', 0)),
                    'relative_velocity_kms': float(approach.relative_velocity.get('kilometers_per_second', 0)),
                    'miss_distance_km': float(approach.miss_distance.get('kilometers', 0)),
                    'miss_distance_lunar': float(approach.miss_distance.get('lunar', 0)),
                    'miss_distance_au': float(approach.miss_distance.get('astronomical', 0)),
                    'orbiting_body': approach.orbiting_body
                })
            
            result.append({
                'neo_id': neo.id,
                'neo_reference_id': neo.neo_reference_id,
                'name': neo.name,
                'nasa_jpl_url': neo.nasa_jpl_url,
                'absolute_magnitude_h': neo.absolute_magnitude_h,
                'estimated_diameter_min_km': neo.estimated_diameter.kilometers.get('estimated_diameter_min', 0),
                'estimated_diameter_max_km': neo.estimated_diameter.kilometers.get('estimated_diameter_max', 0),
                'is_potentially_hazardous': neo.is_potentially_hazardous_asteroid,
                'is_sentry_object': getattr(neo, 'is_sentry_object', False),
                'close_approach_data': close_approaches
            })
        
        self.logger.info(f"Extracted {len(result)} NEO records")
        return result
    
    def extract_neo_stats(self) -> Dict[str, Any]:
        """Extract NEO statistics."""
        self.logger.info("Extracting NEO statistics")
        
        def _extract():
            return self.client.get_neo_stats()
        
        stats = self.extract_with_retry(_extract)
        
        result = {
            'near_earth_object_count': stats['near_earth_object_count'],
            'close_approach_count': stats['close_approach_count'],
            'last_updated': stats['last_updated'],
            'source': stats['source'],
            'nasa_jpl_url': stats['nasa_jpl_url']
        }
        
        self.logger.info("Extracted NEO statistics")
        return result
