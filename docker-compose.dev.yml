version: '3.8'

# Development override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  postgres:
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=nasa_pipeline_dev
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  airflow-webserver:
    environment:
      - AIRFLOW__CORE__EXECUTOR=SequentialExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=sqlite:////opt/airflow/airflow.db
      - AIRFLOW__CORE__LOAD_EXAMPLES=True
      - AIRFLOW__WEBSERVER__RELOAD_ON_PLUGIN_CHANGE=True
    volumes:
      - ./src:/app/src:ro
      - ./dags:/opt/airflow/dags:ro
      - ./logs:/opt/airflow/logs
      - ./data:/opt/airflow/data
      - ./.env:/opt/airflow/.env:ro
    deploy:
      resources:
        limits:
          memory: 768M
        reservations:
          memory: 384M

  airflow-scheduler:
    environment:
      - AIRFLOW__CORE__EXECUTOR=SequentialExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=sqlite:////opt/airflow/airflow.db
      - AIRFLOW__CORE__LOAD_EXAMPLES=True
    volumes:
      - ./src:/app/src:ro
      - ./dags:/opt/airflow/dags:ro
      - ./logs:/opt/airflow/logs
      - ./data:/opt/airflow/data
      - ./.env:/opt/airflow/.env:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  streamlit:
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=nasa_pipeline_dev
    volumes:
      - ./src:/app/src:ro
      - ./data:/app/data
      - ./.env:/app/.env:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Development tools
  jupyter:
    build: .
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token=''
    ports:
      - "8888:8888"
    volumes:
      - ./src:/app/src
      - ./notebooks:/app/notebooks
      - ./data:/app/data
      - ./.env:/app/.env
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=nasa_pipeline_dev
    depends_on:
      - postgres
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

volumes:
  postgres_dev_data:
