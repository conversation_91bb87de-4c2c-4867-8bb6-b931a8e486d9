# Contributing to NASA API Pipeline

Thank you for your interest in contributing to the NASA API Pipeline! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Types of Contributions
- **Bug Reports**: Report issues or bugs
- **Feature Requests**: Suggest new features or improvements
- **Code Contributions**: Submit code changes via pull requests
- **Documentation**: Improve documentation and examples
- **Testing**: Add or improve tests

### Getting Started
1. Fork the repository
2. Clone your fork locally
3. Set up the development environment
4. Create a feature branch
5. Make your changes
6. Test your changes
7. Submit a pull request

## 🛠️ Development Setup

### Prerequisites
- Python 3.11+
- Docker Desktop
- Git
- UV package manager

### Local Development Setup
```bash
# Clone your fork
git clone https://github.com/your-username/NASA_API_Pipeline.git
cd NASA_API_Pipeline

# Install UV if not already installed
pip install uv

# Install dependencies
uv sync

# Install development dependencies
uv sync --group dev

# Set up pre-commit hooks
pre-commit install

# Copy environment file
cp .env.example .env
# Edit .env with your NASA API key
```

### Development Environment
```bash
# Start development services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Access services:
# - Airflow: http://localhost:8080
# - Streamlit: http://localhost:8501
# - Jupyter: http://localhost:8888
# - PostgreSQL: localhost:5432
```

## 📝 Code Style and Standards

### Python Code Style
We follow PEP 8 with some modifications:
- Line length: 88 characters (Black default)
- Use type hints for all functions
- Use docstrings for all public functions and classes
- Use f-strings for string formatting

### Code Formatting
```bash
# Format code with Black
uv run black src/ tests/

# Sort imports with isort
uv run isort src/ tests/

# Check code style with flake8
uv run flake8 src/ tests/

# Type checking with mypy
uv run mypy src/
```

### Pre-commit Hooks
Pre-commit hooks automatically run code formatting and checks:
```bash
# Install pre-commit hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
uv run pytest

# Run specific test file
uv run pytest tests/test_api_clients.py

# Run tests with coverage
uv run pytest --cov=src --cov-report=html

# Run only unit tests (skip integration tests)
uv run pytest -m "not integration"

# Run integration tests (requires internet)
uv run pytest -m integration
```

### Writing Tests
- Write unit tests for all new functions
- Use pytest fixtures for common test data
- Mock external dependencies (APIs, databases)
- Include integration tests for critical paths
- Aim for >80% code coverage

### Test Structure
```python
def test_function_name():
    """Test description."""
    # Arrange
    input_data = {"test": "data"}
    
    # Act
    result = function_under_test(input_data)
    
    # Assert
    assert result == expected_result
```

## 📚 Documentation

### Documentation Standards
- Use clear, concise language
- Include code examples
- Update relevant documentation with code changes
- Use proper Markdown formatting

### Documentation Types
- **README.md**: Project overview and quick start
- **API_DOCUMENTATION.md**: API reference and examples
- **DEPLOYMENT.md**: Deployment instructions
- **CONTRIBUTING.md**: This file
- **Docstrings**: Inline code documentation

### Docstring Format
```python
def function_name(param1: str, param2: int) -> bool:
    """
    Brief description of the function.
    
    Args:
        param1: Description of param1
        param2: Description of param2
        
    Returns:
        Description of return value
        
    Raises:
        ValueError: When param1 is invalid
        
    Example:
        >>> result = function_name("test", 42)
        >>> print(result)
        True
    """
    pass
```

## 🔄 Pull Request Process

### Before Submitting
1. Ensure all tests pass
2. Update documentation if needed
3. Add tests for new functionality
4. Follow code style guidelines
5. Update CHANGELOG.md if applicable

### Pull Request Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

### Review Process
1. Automated checks must pass
2. Code review by maintainers
3. Address feedback if any
4. Approval and merge

## 🐛 Bug Reports

### Bug Report Template
```markdown
## Bug Description
Clear description of the bug

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: Windows 10
- Python version: 3.11
- Docker version: 20.10.x
- Browser (if applicable): Chrome 120.x

## Additional Context
Any other relevant information
```

## 💡 Feature Requests

### Feature Request Template
```markdown
## Feature Description
Clear description of the proposed feature

## Use Case
Why is this feature needed?

## Proposed Solution
How should this feature work?

## Alternatives Considered
Other approaches considered

## Additional Context
Any other relevant information
```

## 🏗️ Architecture Guidelines

### Project Structure
```
NASA_API_Pipeline/
├── src/                    # Source code
│   ├── api/               # NASA API clients
│   ├── config/            # Configuration
│   ├── database/          # Database operations
│   ├── etl/              # ETL pipeline
│   └── dashboard/         # Streamlit dashboard
├── dags/                  # Airflow DAGs
├── tests/                 # Test files
├── sql/                   # Database schemas
├── scripts/               # Utility scripts
└── docs/                  # Documentation
```

### Design Principles
- **Modularity**: Keep components loosely coupled
- **Testability**: Write testable code with dependency injection
- **Configurability**: Use environment variables for configuration
- **Error Handling**: Implement comprehensive error handling
- **Logging**: Use structured logging throughout
- **Performance**: Consider memory usage (6GB RAM constraint)

### Adding New Features

#### New API Endpoint
1. Add client method in `src/api/nasa_client.py`
2. Add data model in `src/api/data_models.py`
3. Add extractor in `src/etl/extractors.py`
4. Add transformer in `src/etl/transformers.py`
5. Add database model in `src/database/models.py`
6. Update database manager in `src/database/manager.py`
7. Add tests for all components

#### New Dashboard Page
1. Add page function in `src/dashboard/app.py`
2. Add navigation option
3. Add required database queries
4. Test with sample data

## 🔒 Security Guidelines

### Security Best Practices
- Never commit API keys or secrets
- Use environment variables for sensitive data
- Validate all user inputs
- Implement proper error handling (don't expose internals)
- Keep dependencies updated

### Reporting Security Issues
Please report security vulnerabilities privately to the maintainers.

## 📋 Code Review Guidelines

### For Authors
- Keep pull requests focused and small
- Write clear commit messages
- Include tests for new functionality
- Update documentation as needed

### For Reviewers
- Be constructive and respectful
- Focus on code quality and maintainability
- Check for security issues
- Verify tests are adequate

## 🎯 Performance Considerations

### Memory Usage
- Target: 6GB RAM total system usage
- Monitor memory usage in Docker containers
- Optimize database queries
- Use pagination for large datasets

### API Usage
- Respect NASA API rate limits
- Implement caching where appropriate
- Use batch operations when possible
- Handle API errors gracefully

## 📞 Getting Help

### Communication Channels
- GitHub Issues: Bug reports and feature requests
- GitHub Discussions: General questions and ideas
- Pull Request Comments: Code-specific discussions

### Resources
- [NASA API Documentation](https://api.nasa.gov/)
- [Apache Airflow Documentation](https://airflow.apache.org/docs/)
- [Streamlit Documentation](https://docs.streamlit.io/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project (MIT License).

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- GitHub contributors page

Thank you for contributing to the NASA API Pipeline! 🚀
