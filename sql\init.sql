-- Initialize databases for NASA API Pipeline

-- Create nasa_pipeline database
CREATE DATABASE nasa_pipeline;

-- Connect to nasa_pipeline database
\c nasa_pipeline;

-- Create schemas
CREATE SCHEMA IF NOT EXISTS raw_data;
CREATE SCHEMA IF NOT EXISTS processed_data;
CREATE SCHEMA IF NOT EXISTS analytics;

-- Create tables for NASA API data

-- Astronomy Picture of the Day (APOD)
CREATE TABLE raw_data.apod (
    id SERIAL PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    explanation TEXT,
    url TEXT,
    hdurl TEXT,
    media_type VARCHAR(50),
    copyright VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON> Rover Photos
CREATE TABLE raw_data.mars_rover_photos (
    id SERIAL PRIMARY KEY,
    photo_id INTEGER UNIQUE NOT NULL,
    sol INTEGER,
    camera_name <PERSON><PERSON><PERSON><PERSON>(100),
    camera_full_name VA<PERSON>HA<PERSON>(200),
    rover_name <PERSON><PERSON><PERSON><PERSON>(50),
    rover_status VARCHAR(50),
    rover_launch_date DATE,
    rover_landing_date DATE,
    earth_date DATE,
    img_src TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Near Earth Objects (NEO)
CREATE TABLE raw_data.neo_objects (
    id SERIAL PRIMARY KEY,
    neo_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200),
    nasa_jpl_url TEXT,
    absolute_magnitude_h DECIMAL(10,6),
    estimated_diameter_min_km DECIMAL(15,10),
    estimated_diameter_max_km DECIMAL(15,10),
    is_potentially_hazardous BOOLEAN,
    close_approach_date DATE,
    relative_velocity_kmh DECIMAL(15,6),
    miss_distance_km DECIMAL(20,6),
    orbiting_body VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Processed data tables
CREATE TABLE processed_data.daily_summary (
    id SERIAL PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    apod_count INTEGER DEFAULT 0,
    mars_photos_count INTEGER DEFAULT 0,
    neo_count INTEGER DEFAULT 0,
    hazardous_neo_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Analytics tables
CREATE TABLE analytics.popular_cameras (
    id SERIAL PRIMARY KEY,
    camera_name VARCHAR(100),
    rover_name VARCHAR(50),
    photo_count INTEGER,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_apod_date ON raw_data.apod(date);
CREATE INDEX idx_mars_rover_earth_date ON raw_data.mars_rover_photos(earth_date);
CREATE INDEX idx_mars_rover_name ON raw_data.mars_rover_photos(rover_name);
CREATE INDEX idx_neo_close_approach_date ON raw_data.neo_objects(close_approach_date);
CREATE INDEX idx_neo_hazardous ON raw_data.neo_objects(is_potentially_hazardous);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_apod_updated_at BEFORE UPDATE ON raw_data.apod
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
