"""Base API client with common functionality."""

import time
from typing import Any, Dict, Optional
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import logging

from src.config.settings import settings


class BaseAPIClient:
    """Base API client with retry logic and rate limiting."""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url
        self.api_key = api_key or settings.nasa_api_key
        self.session = self._create_session()
        self.logger = logging.getLogger(self.__class__.__name__)
        self._last_request_time = 0
        self._min_request_interval = 3600 / settings.api_rate_limit  # seconds between requests
    
    def _create_session(self) -> requests.Session:
        """Create a requests session with retry strategy."""
        session = requests.Session()
        
        # Define retry strategy
        retry_strategy = Retry(
            total=settings.api_retry_attempts,
            backoff_factor=settings.api_retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        # Mount adapter with retry strategy
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _rate_limit(self) -> None:
        """Implement rate limiting."""
        current_time = time.time()
        time_since_last_request = current_time - self._last_request_time
        
        if time_since_last_request < self._min_request_interval:
            sleep_time = self._min_request_interval - time_since_last_request
            self.logger.info(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    def _make_request(
        self, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        timeout: int = 30
    ) -> Dict[str, Any]:
        """Make an API request with error handling."""
        self._rate_limit()
        
        # Add API key to parameters
        if params is None:
            params = {}
        params["api_key"] = self.api_key
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            self.logger.info(f"Making request to {url} with params: {params}")
            response = self.session.get(url, params=params, timeout=timeout)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error occurred: {e}")
            if response.status_code == 429:
                self.logger.warning("Rate limit exceeded, waiting longer...")
                time.sleep(60)  # Wait 1 minute for rate limit reset
                return self._make_request(endpoint, params, timeout)
            raise
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            raise
            
        except ValueError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            raise
    
    def health_check(self) -> bool:
        """Check if the API is accessible."""
        try:
            # Try a simple request to verify connectivity
            self._make_request("planetary/apod", {"count": 1})
            return True
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
