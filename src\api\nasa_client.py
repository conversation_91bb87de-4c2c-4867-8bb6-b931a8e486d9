"""NASA API client for all endpoints."""

from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
import logging

from .base_client import BaseAPIClient
from src.config.settings import settings


class NASAClient(BaseAPIClient):
    """NASA API client for APOD, Mars Rover Photos, and NEO data."""
    
    def __init__(self):
        super().__init__(settings.nasa_api_base_url, settings.nasa_api_key)
        self.logger = logging.getLogger(__name__)
    
    # APOD (Astronomy Picture of the Day) Methods
    def get_apod(
        self, 
        date: Optional[str] = None, 
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        count: Optional[int] = None,
        thumbs: bool = False
    ) -> Dict[str, Any]:
        """
        Get Astronomy Picture of the Day data.
        
        Args:
            date: YYYY-MM-DD format for specific date
            start_date: YYYY-MM-DD format for date range start
            end_date: YYYY-MM-DD format for date range end
            count: Number of random images to return
            thumbs: Return thumbnail URL for video content
            
        Returns:
            Dictionary containing APOD data
        """
        params = {}
        
        if date:
            params["date"] = date
        elif start_date and end_date:
            params["start_date"] = start_date
            params["end_date"] = end_date
        elif count:
            params["count"] = count
            
        if thumbs:
            params["thumbs"] = "true"
            
        return self._make_request("planetary/apod", params)
    
    def get_apod_range(self, days_back: int = 7) -> List[Dict[str, Any]]:
        """
        Get APOD data for the last N days.
        
        Args:
            days_back: Number of days to go back from today
            
        Returns:
            List of APOD data dictionaries
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days_back)
        
        response = self.get_apod(
            start_date=start_date.strftime("%Y-%m-%d"),
            end_date=end_date.strftime("%Y-%m-%d")
        )
        
        # Ensure we return a list
        if isinstance(response, dict):
            return [response]
        return response
    
    # Mars Rover Photos Methods
    def get_mars_rover_photos(
        self,
        rover: str = "curiosity",
        sol: Optional[int] = None,
        earth_date: Optional[str] = None,
        camera: Optional[str] = None,
        page: int = 1
    ) -> Dict[str, Any]:
        """
        Get Mars rover photos.
        
        Args:
            rover: Rover name (curiosity, opportunity, spirit)
            sol: Martian sol (day) number
            earth_date: Earth date in YYYY-MM-DD format
            camera: Camera name (FHAZ, RHAZ, MAST, CHEMCAM, MAHLI, MARDI, NAVCAM)
            page: Page number for pagination
            
        Returns:
            Dictionary containing photos data
        """
        params = {"page": page}
        
        if sol is not None:
            params["sol"] = sol
        elif earth_date:
            params["earth_date"] = earth_date
        else:
            # Default to latest sol
            params["sol"] = 1000
            
        if camera:
            params["camera"] = camera.lower()
            
        endpoint = f"mars-photos/api/v1/rovers/{rover.lower()}/photos"
        return self._make_request(endpoint, params)
    
    def get_rover_manifest(self, rover: str = "curiosity") -> Dict[str, Any]:
        """
        Get rover mission manifest.
        
        Args:
            rover: Rover name (curiosity, opportunity, spirit)
            
        Returns:
            Dictionary containing rover manifest data
        """
        endpoint = f"mars-photos/api/v1/manifests/{rover.lower()}"
        return self._make_request(endpoint)
    
    def get_latest_rover_photos(
        self, 
        rover: str = "curiosity", 
        camera: Optional[str] = None,
        limit: int = 25
    ) -> List[Dict[str, Any]]:
        """
        Get the latest photos from a Mars rover.
        
        Args:
            rover: Rover name
            camera: Specific camera to filter by
            limit: Maximum number of photos to return
            
        Returns:
            List of photo dictionaries
        """
        # Get rover manifest to find latest sol
        manifest = self.get_rover_manifest(rover)
        latest_sol = manifest["photo_manifest"]["max_sol"]
        
        # Get photos from the latest sol
        photos_data = self.get_mars_rover_photos(
            rover=rover,
            sol=latest_sol,
            camera=camera
        )
        
        photos = photos_data.get("photos", [])
        return photos[:limit]
    
    # NEO (Near Earth Objects) Methods
    def get_neo_feed(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get Near Earth Objects feed.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            
        Returns:
            Dictionary containing NEO feed data
        """
        params = {}
        
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date
            
        return self._make_request("neo/rest/v1/feed", params)
    
    def get_neo_lookup(self, neo_id: str) -> Dict[str, Any]:
        """
        Lookup a specific Near Earth Object by ID.
        
        Args:
            neo_id: NEO ID (e.g., "3542519")
            
        Returns:
            Dictionary containing NEO data
        """
        endpoint = f"neo/rest/v1/neo/{neo_id}"
        return self._make_request(endpoint)
    
    def get_neo_browse(self, page: int = 0, size: int = 20) -> Dict[str, Any]:
        """
        Browse the overall NEO dataset.
        
        Args:
            page: Page number
            size: Number of NEOs per page
            
        Returns:
            Dictionary containing paginated NEO data
        """
        params = {"page": page, "size": size}
        return self._make_request("neo/rest/v1/neo/browse", params)
    
    def get_neo_stats(self) -> Dict[str, Any]:
        """
        Get NEO dataset statistics.
        
        Returns:
            Dictionary containing NEO statistics
        """
        return self._make_request("neo/rest/v1/stats")
    
    def get_recent_neo_feed(self, days: int = 7) -> Dict[str, Any]:
        """
        Get NEO feed for recent days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            Dictionary containing recent NEO data
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        return self.get_neo_feed(
            start_date=start_date.strftime("%Y-%m-%d"),
            end_date=end_date.strftime("%Y-%m-%d")
        )
