"""
NASA API Pipeline Dashboard

Interactive Streamlit dashboard for visualizing NASA data
including APOD, Mars rover photos, and NEO information.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date, timedelta
import sys
import os
from PIL import Image
import requests
from io import BytesIO

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.database.manager import DatabaseManager
from src.etl.pipeline import ETLPipeline
from src.config.settings import settings


# Page configuration
st.set_page_config(
    page_title="NASA API Pipeline Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .sidebar-info {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


@st.cache_data(ttl=300)  # Cache for 5 minutes
def load_database_data():
    """Load data from database with caching."""
    try:
        db_manager = DatabaseManager()
        
        # Check database health
        if not db_manager.health_check():
            st.error("Database connection failed!")
            return None, None, None, None
        
        # Get recent APOD data
        recent_apod = db_manager.get_recent_apod(days=30)
        
        # Get NEO statistics
        neo_stats = db_manager.get_neo_statistics()
        
        # Get hazardous NEOs
        hazardous_neos = db_manager.get_hazardous_neos()
        
        # Get recent rover photos (this would need to be implemented in DatabaseManager)
        # For now, we'll use a placeholder
        rover_photos = []
        
        return recent_apod, neo_stats, hazardous_neos, rover_photos
        
    except Exception as e:
        st.error(f"Error loading data: {e}")
        return None, None, None, None


def display_header():
    """Display the main header."""
    st.markdown('<h1 class="main-header">🚀 NASA API Pipeline Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("---")


def display_sidebar():
    """Display sidebar with navigation and info."""
    st.sidebar.title("Navigation")
    
    page = st.sidebar.selectbox(
        "Select Page",
        ["Overview", "APOD Gallery", "Mars Exploration", "NEO Tracker", "Pipeline Status"]
    )
    
    st.sidebar.markdown("---")
    
    # Pipeline controls
    st.sidebar.subheader("Pipeline Controls")
    
    if st.sidebar.button("Run Daily Pipeline"):
        with st.spinner("Running daily pipeline..."):
            try:
                pipeline = ETLPipeline()
                results = pipeline.run_daily_pipeline()
                st.sidebar.success("Daily pipeline completed!")
                st.sidebar.json(results)
            except Exception as e:
                st.sidebar.error(f"Pipeline failed: {e}")
    
    if st.sidebar.button("Health Check"):
        with st.spinner("Checking system health..."):
            try:
                pipeline = ETLPipeline()
                health = pipeline.health_check()
                if health['overall']:
                    st.sidebar.success("All systems healthy!")
                else:
                    st.sidebar.warning("Some systems have issues")
                st.sidebar.json(health)
            except Exception as e:
                st.sidebar.error(f"Health check failed: {e}")
    
    # Info section
    st.sidebar.markdown("---")
    st.sidebar.markdown("""
    <div class="sidebar-info">
    <h4>About This Dashboard</h4>
    <p>This dashboard visualizes data from NASA's public APIs including:</p>
    <ul>
    <li>Astronomy Picture of the Day (APOD)</li>
    <li>Mars Rover Photos</li>
    <li>Near Earth Objects (NEO)</li>
    </ul>
    <p>Data is automatically updated daily via Apache Airflow.</p>
    </div>
    """, unsafe_allow_html=True)
    
    return page


def display_overview(apod_data, neo_stats, hazardous_neos, rover_photos):
    """Display overview page."""
    st.header("📊 Overview")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="APOD Records (30 days)",
            value=len(apod_data) if apod_data else 0,
            delta=None
        )
    
    with col2:
        st.metric(
            label="Total NEOs",
            value=neo_stats.get('total_neo_count', 0) if neo_stats else 0,
            delta=None
        )
    
    with col3:
        st.metric(
            label="Hazardous NEOs",
            value=len(hazardous_neos) if hazardous_neos else 0,
            delta=None
        )
    
    with col4:
        st.metric(
            label="Rover Photos",
            value=len(rover_photos) if rover_photos else 0,
            delta=None
        )
    
    st.markdown("---")
    
    # Charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("APOD Data Trend")
        if apod_data:
            # Create DataFrame for plotting
            apod_df = pd.DataFrame([{
                'date': apod.date,
                'media_type': apod.media_type,
                'has_copyright': bool(apod.copyright)
            } for apod in apod_data])
            
            # Group by date and count
            daily_counts = apod_df.groupby('date').size().reset_index(name='count')
            
            fig = px.line(daily_counts, x='date', y='count', 
                         title="Daily APOD Records")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No APOD data available")
    
    with col2:
        st.subheader("NEO Hazard Distribution")
        if neo_stats:
            hazard_data = {
                'Type': ['Safe', 'Potentially Hazardous'],
                'Count': [
                    neo_stats.get('total_neo_count', 0) - neo_stats.get('hazardous_neo_count', 0),
                    neo_stats.get('hazardous_neo_count', 0)
                ]
            }
            
            fig = px.pie(values=hazard_data['Count'], names=hazard_data['Type'],
                        title="NEO Hazard Distribution")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No NEO statistics available")


def display_apod_gallery(apod_data):
    """Display APOD gallery page."""
    st.header("🌌 Astronomy Picture of the Day Gallery")
    
    if not apod_data:
        st.info("No APOD data available. Run the pipeline to fetch data.")
        return
    
    # Filters
    col1, col2 = st.columns(2)
    
    with col1:
        media_types = list(set([apod.media_type for apod in apod_data]))
        selected_media = st.selectbox("Filter by Media Type", ["All"] + media_types)
    
    with col2:
        date_range = st.date_input(
            "Date Range",
            value=(date.today() - timedelta(days=7), date.today()),
            max_value=date.today()
        )
    
    # Filter data
    filtered_data = apod_data
    if selected_media != "All":
        filtered_data = [apod for apod in filtered_data if apod.media_type == selected_media]
    
    if len(date_range) == 2:
        start_date, end_date = date_range
        filtered_data = [apod for apod in filtered_data 
                        if start_date <= apod.date <= end_date]
    
    # Display gallery
    st.subheader(f"Showing {len(filtered_data)} records")
    
    for apod in filtered_data[:10]:  # Limit to 10 for performance
        with st.expander(f"{apod.date} - {apod.title}"):
            col1, col2 = st.columns([1, 2])
            
            with col1:
                if apod.media_type == "image" and apod.url:
                    try:
                        response = requests.get(apod.url, timeout=10)
                        if response.status_code == 200:
                            image = Image.open(BytesIO(response.content))
                            st.image(image, caption=apod.title, use_column_width=True)
                    except Exception as e:
                        st.error(f"Could not load image: {e}")
                        st.write(f"Image URL: {apod.url}")
                else:
                    st.write(f"Media Type: {apod.media_type}")
                    if apod.url:
                        st.write(f"[View Media]({apod.url})")
            
            with col2:
                st.write(f"**Date:** {apod.date}")
                st.write(f"**Title:** {apod.title}")
                if apod.copyright:
                    st.write(f"**Copyright:** {apod.copyright}")
                st.write(f"**Explanation:** {apod.explanation[:500]}...")
                if apod.hdurl:
                    st.write(f"[High Resolution Image]({apod.hdurl})")


def display_mars_exploration(rover_photos):
    """Display Mars exploration page."""
    st.header("🔴 Mars Exploration")
    
    st.info("Mars rover photo functionality will be implemented when rover photo data is available in the database.")
    
    # Placeholder content
    st.subheader("Rover Information")
    
    rover_info = {
        "Curiosity": {
            "Status": "Active",
            "Landing Date": "2012-08-05",
            "Mission Duration": "11+ years",
            "Key Instruments": ["ChemCam", "MAHLI", "MARDI", "MastCam"]
        },
        "Opportunity": {
            "Status": "Mission Complete",
            "Landing Date": "2004-01-25",
            "Mission Duration": "15 years",
            "Key Instruments": ["PanCam", "NavCam", "HazCam"]
        },
        "Spirit": {
            "Status": "Mission Complete",
            "Landing Date": "2004-01-04",
            "Mission Duration": "6 years",
            "Key Instruments": ["PanCam", "NavCam", "HazCam"]
        }
    }
    
    for rover, info in rover_info.items():
        with st.expander(f"{rover} Rover"):
            for key, value in info.items():
                if isinstance(value, list):
                    st.write(f"**{key}:** {', '.join(value)}")
                else:
                    st.write(f"**{key}:** {value}")


def display_neo_tracker(neo_stats, hazardous_neos):
    """Display NEO tracker page."""
    st.header("☄️ Near Earth Object Tracker")
    
    if not neo_stats and not hazardous_neos:
        st.info("No NEO data available. Run the pipeline to fetch data.")
        return
    
    # Statistics
    if neo_stats:
        st.subheader("NEO Statistics")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total NEOs", neo_stats.get('total_neo_count', 0))
        
        with col2:
            st.metric("Hazardous NEOs", neo_stats.get('hazardous_neo_count', 0))
        
        with col3:
            hazard_percentage = neo_stats.get('hazardous_percentage', 0)
            st.metric("Hazard Percentage", f"{hazard_percentage:.1f}%")
    
    # Hazardous NEOs
    if hazardous_neos:
        st.subheader("Potentially Hazardous NEOs")
        
        # Create DataFrame
        neo_df = pd.DataFrame([{
            'Name': neo.name,
            'NEO ID': neo.neo_id,
            'Absolute Magnitude': float(neo.absolute_magnitude_h) if neo.absolute_magnitude_h else 0,
            'Est. Diameter (km)': f"{float(neo.estimated_diameter_min_km) if neo.estimated_diameter_min_km else 0:.3f} - {float(neo.estimated_diameter_max_km) if neo.estimated_diameter_max_km else 0:.3f}",
            'NASA JPL URL': neo.nasa_jpl_url
        } for neo in hazardous_neos[:50]])  # Limit to 50 for performance
        
        st.dataframe(neo_df, use_container_width=True)
        
        # Size distribution chart
        if len(hazardous_neos) > 0:
            sizes = [float(neo.estimated_diameter_max_km) if neo.estimated_diameter_max_km else 0 
                    for neo in hazardous_neos if neo.estimated_diameter_max_km]
            
            if sizes:
                fig = px.histogram(x=sizes, nbins=20, 
                                 title="Size Distribution of Hazardous NEOs",
                                 labels={'x': 'Diameter (km)', 'y': 'Count'})
                st.plotly_chart(fig, use_container_width=True)


def display_pipeline_status():
    """Display pipeline status page."""
    st.header("⚙️ Pipeline Status")
    
    # System health
    st.subheader("System Health")
    
    try:
        pipeline = ETLPipeline()
        health = pipeline.health_check()
        
        col1, col2 = st.columns(2)
        
        with col1:
            if health.get('database', False):
                st.success("✅ Database: Healthy")
            else:
                st.error("❌ Database: Unhealthy")
        
        with col2:
            if health.get('nasa_api', False):
                st.success("✅ NASA API: Healthy")
            else:
                st.error("❌ NASA API: Unhealthy")
        
        # Overall status
        if health.get('overall', False):
            st.success("🟢 Overall System Status: Healthy")
        else:
            st.warning("🟡 Overall System Status: Issues Detected")
    
    except Exception as e:
        st.error(f"Could not check system health: {e}")
    
    # Configuration
    st.subheader("Configuration")
    
    config_info = {
        "NASA API Base URL": settings.nasa_api_base_url,
        "Database Host": settings.postgres_host,
        "Database Port": settings.postgres_port,
        "Database Name": settings.postgres_db,
        "Airflow Home": settings.airflow_home,
        "API Rate Limit": f"{settings.api_rate_limit} requests/hour"
    }
    
    for key, value in config_info.items():
        st.write(f"**{key}:** {value}")


def main():
    """Main application function."""
    display_header()
    
    # Load data
    apod_data, neo_stats, hazardous_neos, rover_photos = load_database_data()
    
    # Sidebar navigation
    page = display_sidebar()
    
    # Display selected page
    if page == "Overview":
        display_overview(apod_data, neo_stats, hazardous_neos, rover_photos)
    elif page == "APOD Gallery":
        display_apod_gallery(apod_data)
    elif page == "Mars Exploration":
        display_mars_exploration(rover_photos)
    elif page == "NEO Tracker":
        display_neo_tracker(neo_stats, hazardous_neos)
    elif page == "Pipeline Status":
        display_pipeline_status()


if __name__ == "__main__":
    main()
