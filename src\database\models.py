"""SQLAlchemy models for NASA API data."""

from datetime import datetime
from sqlalchemy import (
    Column, Integer, String, Text, Date, DateTime, Boolean, 
    Decimal, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class APODModel(Base):
    """Astronomy Picture of the Day model."""
    
    __tablename__ = "apod"
    __table_args__ = (
        {"schema": "raw_data"},
        Index("idx_apod_date", "date"),
        Index("idx_apod_media_type", "media_type"),
    )
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, unique=True, nullable=False)
    title = Column(String(500), nullable=False)
    explanation = Column(Text)
    url = Column(Text)
    hdurl = Column(Text)
    media_type = Column(String(50))
    copyright = Column(String(200))
    service_version = Column(String(10))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<APOD(date={self.date}, title='{self.title[:50]}...')>"


class MarsRoverModel(Base):
    """Mars rover model."""
    
    __tablename__ = "mars_rovers"
    __table_args__ = (
        {"schema": "raw_data"},
        Index("idx_rover_name", "name"),
    )
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    landing_date = Column(Date)
    launch_date = Column(Date)
    status = Column(String(50))
    max_sol = Column(Integer)
    max_date = Column(Date)
    total_photos = Column(Integer)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    photos = relationship("MarsRoverPhotoModel", back_populates="rover")
    cameras = relationship("MarsRoverCameraModel", back_populates="rover")
    
    def __repr__(self):
        return f"<MarsRover(name='{self.name}', status='{self.status}')>"


class MarsRoverCameraModel(Base):
    """Mars rover camera model."""
    
    __tablename__ = "mars_rover_cameras"
    __table_args__ = (
        {"schema": "raw_data"},
        Index("idx_camera_rover", "rover_id"),
        Index("idx_camera_name", "name"),
    )
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    full_name = Column(String(200))
    rover_id = Column(Integer, ForeignKey("raw_data.mars_rovers.id"))
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    rover = relationship("MarsRoverModel", back_populates="cameras")
    photos = relationship("MarsRoverPhotoModel", back_populates="camera")
    
    def __repr__(self):
        return f"<MarsRoverCamera(name='{self.name}', rover_id={self.rover_id})>"


class MarsRoverPhotoModel(Base):
    """Mars rover photo model."""
    
    __tablename__ = "mars_rover_photos"
    __table_args__ = (
        {"schema": "raw_data"},
        Index("idx_photo_earth_date", "earth_date"),
        Index("idx_photo_sol", "sol"),
        Index("idx_photo_rover", "rover_id"),
        Index("idx_photo_camera", "camera_id"),
    )
    
    id = Column(Integer, primary_key=True)
    photo_id = Column(Integer, unique=True, nullable=False)
    sol = Column(Integer)
    earth_date = Column(Date)
    img_src = Column(Text)
    rover_id = Column(Integer, ForeignKey("raw_data.mars_rovers.id"))
    camera_id = Column(Integer, ForeignKey("raw_data.mars_rover_cameras.id"))
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    rover = relationship("MarsRoverModel", back_populates="photos")
    camera = relationship("MarsRoverCameraModel", back_populates="photos")
    
    def __repr__(self):
        return f"<MarsRoverPhoto(photo_id={self.photo_id}, sol={self.sol})>"


class NEOObjectModel(Base):
    """Near Earth Object model."""
    
    __tablename__ = "neo_objects"
    __table_args__ = (
        {"schema": "raw_data"},
        Index("idx_neo_reference_id", "neo_reference_id"),
        Index("idx_neo_hazardous", "is_potentially_hazardous"),
        Index("idx_neo_magnitude", "absolute_magnitude_h"),
    )
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    neo_id = Column(String(50), unique=True, nullable=False)
    neo_reference_id = Column(String(50))
    name = Column(String(200))
    nasa_jpl_url = Column(Text)
    absolute_magnitude_h = Column(Decimal(10, 6))
    estimated_diameter_min_km = Column(Decimal(15, 10))
    estimated_diameter_max_km = Column(Decimal(15, 10))
    is_potentially_hazardous = Column(Boolean, default=False)
    is_sentry_object = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    close_approaches = relationship("NEOCloseApproachModel", back_populates="neo_object")
    
    def __repr__(self):
        return f"<NEOObject(name='{self.name}', hazardous={self.is_potentially_hazardous})>"


class NEOCloseApproachModel(Base):
    """NEO close approach model."""
    
    __tablename__ = "neo_close_approaches"
    __table_args__ = (
        {"schema": "raw_data"},
        Index("idx_approach_date", "close_approach_date"),
        Index("idx_approach_neo", "neo_object_id"),
        Index("idx_approach_distance", "miss_distance_km"),
    )
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    neo_object_id = Column(Integer, ForeignKey("raw_data.neo_objects.id"))
    close_approach_date = Column(Date)
    close_approach_date_full = Column(String(50))
    epoch_date_close_approach = Column(Integer)
    relative_velocity_kmh = Column(Decimal(15, 6))
    relative_velocity_kms = Column(Decimal(15, 6))
    miss_distance_km = Column(Decimal(20, 6))
    miss_distance_lunar = Column(Decimal(15, 6))
    miss_distance_au = Column(Decimal(15, 10))
    orbiting_body = Column(String(50))
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    neo_object = relationship("NEOObjectModel", back_populates="close_approaches")
    
    def __repr__(self):
        return f"<NEOCloseApproach(date={self.close_approach_date}, distance_km={self.miss_distance_km})>"


# Processed Data Models
class DailySummaryModel(Base):
    """Daily summary model for processed data."""
    
    __tablename__ = "daily_summary"
    __table_args__ = (
        {"schema": "processed_data"},
        Index("idx_summary_date", "date"),
    )
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, unique=True, nullable=False)
    apod_count = Column(Integer, default=0)
    mars_photos_count = Column(Integer, default=0)
    neo_count = Column(Integer, default=0)
    hazardous_neo_count = Column(Integer, default=0)
    total_api_calls = Column(Integer, default=0)
    data_quality_score = Column(Decimal(5, 2))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<DailySummary(date={self.date}, neo_count={self.neo_count})>"


# Analytics Models
class PopularCameraModel(Base):
    """Popular camera analytics model."""
    
    __tablename__ = "popular_cameras"
    __table_args__ = (
        {"schema": "analytics"},
        Index("idx_camera_stats", "camera_name", "rover_name"),
    )
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    camera_name = Column(String(100))
    camera_full_name = Column(String(200))
    rover_name = Column(String(50))
    photo_count = Column(Integer, default=0)
    avg_photos_per_sol = Column(Decimal(10, 2))
    first_photo_date = Column(Date)
    last_photo_date = Column(Date)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<PopularCamera(camera='{self.camera_name}', rover='{self.rover_name}', count={self.photo_count})>"


class NEOStatisticsModel(Base):
    """NEO statistics model."""
    
    __tablename__ = "neo_statistics"
    __table_args__ = (
        {"schema": "analytics"},
        Index("idx_neo_stats_date", "calculation_date"),
    )
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    calculation_date = Column(Date, default=func.current_date())
    total_neo_count = Column(Integer)
    hazardous_neo_count = Column(Integer)
    avg_diameter_km = Column(Decimal(15, 10))
    closest_approach_km = Column(Decimal(20, 6))
    largest_neo_diameter_km = Column(Decimal(15, 10))
    upcoming_approaches_30_days = Column(Integer)
    created_at = Column(DateTime, default=func.now())
    
    def __repr__(self):
        return f"<NEOStatistics(date={self.calculation_date}, total={self.total_neo_count})>"
