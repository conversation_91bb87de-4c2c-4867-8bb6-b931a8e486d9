FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV AIRFLOW_HOME=/opt/airflow

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install UV package manager
RUN pip install uv

# Set working directory
WORKDIR /app

# Copy project files
COPY pyproject.toml ./
COPY src/ ./src/
COPY dags/ ./dags/
COPY main.py ./

# Install Python dependencies using UV
RUN uv pip install --system -e .

# Create airflow user and directories
RUN useradd -m -u 50000 airflow && \
    mkdir -p /opt/airflow/dags /opt/airflow/logs /opt/airflow/data && \
    chown -R airflow:airflow /opt/airflow /app

# Switch to airflow user
USER airflow

# Initialize Airflow database (will be overridden in docker-compose)
RUN airflow db init

# Expose ports
EXPOSE 8080 8501

# Default command
CMD ["uv", "run", "main.py"]
