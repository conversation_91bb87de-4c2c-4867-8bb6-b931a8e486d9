"""
NASA API Pipeline - Main Entry Point

This script serves as the main entry point for the NASA API data pipeline.
It can be used to run various pipeline operations including:
- Data extraction from NASA APIs
- ETL processes
- Database operations
- Manual pipeline execution

Usage:
    uv run main.py [command] [options]

Commands:
    extract     - Extract data from NASA APIs
    transform   - Transform and clean data
    load        - Load data into database
    pipeline    - Run full ETL pipeline
    dashboard   - Launch Streamlit dashboard
    setup       - Initialize database and create tables
"""

import argparse
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.settings import settings


def setup_database():
    """Initialize database and create tables."""
    print("Setting up database...")
    try:
        from src.database.manager import DatabaseManager

        db_manager = DatabaseManager()

        # Check if database is accessible
        if not db_manager.health_check():
            print("❌ Database connection failed!")
            print("Please ensure PostgreSQL is running and accessible.")
            return False

        # Create tables
        db_manager.create_tables()
        print("✅ Database tables created successfully!")

        # Test basic operations
        print("🧪 Testing database operations...")

        return True

    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False


def extract_data():
    """Extract data from NASA APIs."""
    print("Extracting data from NASA APIs...")
    try:
        from src.etl.extractors import APODExtractor, NEOExtractor

        # Extract APOD data
        print("📸 Extracting APOD data...")
        apod_extractor = APODExtractor()
        apod_data = apod_extractor.extract_daily_apod()
        print(f"✅ Extracted {len(apod_data)} APOD records")

        # Extract NEO data
        print("☄️ Extracting NEO data...")
        neo_extractor = NEOExtractor()
        neo_data = neo_extractor.extract_recent_neo_feed(days=3)
        print(f"✅ Extracted {len(neo_data)} NEO records")

        print("✅ Data extraction completed!")
        return True

    except Exception as e:
        print(f"❌ Data extraction failed: {e}")
        return False


def transform_data():
    """Transform and clean data."""
    print("Transforming data...")
    try:
        from src.etl.transformers import DataQualityChecker

        # This is a placeholder for transformation logic
        # In a real scenario, you would load raw data and transform it
        quality_checker = DataQualityChecker()

        print("🔄 Data transformation logic would run here...")
        print("✅ Data transformation completed!")
        return True

    except Exception as e:
        print(f"❌ Data transformation failed: {e}")
        return False


def load_data():
    """Load data into database."""
    print("Loading data into database...")
    try:
        # This is a placeholder for loading logic
        # In a real scenario, you would load transformed data into the database
        print("📊 Data loading logic would run here...")
        print("✅ Data loading completed!")
        return True

    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return False


def run_pipeline():
    """Run the complete ETL pipeline."""
    print("🚀 Running NASA API Pipeline...")
    print("=" * 50)

    try:
        from src.etl.pipeline import ETLPipeline

        # Initialize pipeline
        pipeline = ETLPipeline()

        # Check system health first
        print("🏥 Checking system health...")
        health = pipeline.health_check()

        if not health.get('overall', False):
            print("❌ System health check failed!")
            print("Please ensure all services are running properly.")
            return False

        print("✅ System health check passed!")

        # Run the pipeline
        print("🔄 Running ETL pipeline...")
        results = pipeline.run_daily_pipeline()

        # Display results
        print("\n📊 Pipeline Results:")
        print("-" * 30)

        if 'apod_results' in results:
            apod_count = results['apod_results'].get('records_inserted', 0)
            print(f"📸 APOD records processed: {apod_count}")

        if 'neo_results' in results:
            neo_count = results['neo_results'].get('records_inserted', 0)
            print(f"☄️ NEO records processed: {neo_count}")

        if results.get('errors'):
            print(f"⚠️ Errors encountered: {len(results['errors'])}")
            for error in results['errors']:
                print(f"   - {error}")

        print("\n✅ Pipeline execution completed!")
        return True

    except Exception as e:
        print(f"❌ Pipeline execution failed: {e}")
        return False


def launch_dashboard():
    """Launch Streamlit dashboard."""
    import subprocess
    print(f"Launching Streamlit dashboard on port {settings.streamlit_port}...")
    subprocess.run([
        "streamlit", "run", "src/dashboard/app.py",
        "--server.port", str(settings.streamlit_port)
    ])


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="NASA API Pipeline")
    parser.add_argument(
        "command",
        choices=["extract", "transform", "load", "pipeline", "dashboard", "setup"],
        nargs="?",
        default="pipeline",
        help="Command to execute (default: pipeline)"
    )

    args = parser.parse_args()

    print(f"NASA API Pipeline v0.1.0")
    print(f"Command: {args.command}")
    print("-" * 50)

    if args.command == "setup":
        setup_database()
    elif args.command == "extract":
        extract_data()
    elif args.command == "transform":
        transform_data()
    elif args.command == "load":
        load_data()
    elif args.command == "pipeline":
        run_pipeline()
    elif args.command == "dashboard":
        launch_dashboard()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
