"""
NASA API Pipeline - Main Entry Point

This script serves as the main entry point for the NASA API data pipeline.
It can be used to run various pipeline operations including:
- Data extraction from NASA APIs
- ETL processes
- Database operations
- Manual pipeline execution

Usage:
    uv run main.py [command] [options]

Commands:
    extract     - Extract data from NASA APIs
    transform   - Transform and clean data
    load        - Load data into database
    pipeline    - Run full ETL pipeline
    dashboard   - Launch Streamlit dashboard
    setup       - Initialize database and create tables
"""

import argparse
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.settings import settings


def setup_database():
    """Initialize database and create tables."""
    print("Setting up database...")
    # This will be implemented when we create the database module
    print("Database setup completed!")


def extract_data():
    """Extract data from NASA APIs."""
    print("Extracting data from NASA APIs...")
    # This will be implemented when we create the API module
    print("Data extraction completed!")


def transform_data():
    """Transform and clean data."""
    print("Transforming data...")
    # This will be implemented when we create the ETL module
    print("Data transformation completed!")


def load_data():
    """Load data into database."""
    print("Loading data into database...")
    # This will be implemented when we create the database module
    print("Data loading completed!")


def run_pipeline():
    """Run the complete ETL pipeline."""
    print("Running NASA API Pipeline...")
    extract_data()
    transform_data()
    load_data()
    print("Pipeline execution completed!")


def launch_dashboard():
    """Launch Streamlit dashboard."""
    import subprocess
    print(f"Launching Streamlit dashboard on port {settings.streamlit_port}...")
    subprocess.run([
        "streamlit", "run", "src/dashboard/app.py",
        "--server.port", str(settings.streamlit_port)
    ])


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="NASA API Pipeline")
    parser.add_argument(
        "command",
        choices=["extract", "transform", "load", "pipeline", "dashboard", "setup"],
        nargs="?",
        default="pipeline",
        help="Command to execute (default: pipeline)"
    )

    args = parser.parse_args()

    print(f"NASA API Pipeline v0.1.0")
    print(f"Command: {args.command}")
    print("-" * 50)

    if args.command == "setup":
        setup_database()
    elif args.command == "extract":
        extract_data()
    elif args.command == "transform":
        transform_data()
    elif args.command == "load":
        load_data()
    elif args.command == "pipeline":
        run_pipeline()
    elif args.command == "dashboard":
        launch_dashboard()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
