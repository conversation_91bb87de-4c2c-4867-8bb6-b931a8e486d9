"""Tests for NASA API clients."""

import pytest
from unittest.mock import Mock, patch
from datetime import date, datetime
import requests

from src.api.nasa_client import NASAClient
from src.api.base_client import BaseAPIClient
from src.api.utils import validate_date_format, parse_apod_response


class TestBaseAPIClient:
    """Test base API client functionality."""
    
    def test_init(self):
        """Test client initialization."""
        client = BaseAPIClient("https://api.nasa.gov", "test_key")
        assert client.base_url == "https://api.nasa.gov"
        assert client.api_key == "test_key"
        assert client.session is not None
    
    @patch('requests.Session.get')
    def test_make_request_success(self, mock_get):
        """Test successful API request."""
        mock_response = Mock()
        mock_response.json.return_value = {"test": "data"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        client = BaseAPIClient("https://api.nasa.gov", "test_key")
        result = client._make_request("test/endpoint")
        
        assert result == {"test": "data"}
        mock_get.assert_called_once()
    
    @patch('requests.Session.get')
    def test_make_request_http_error(self, mock_get):
        """Test HTTP error handling."""
        mock_response = Mock()
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Not Found")
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        client = BaseAPIClient("https://api.nasa.gov", "test_key")
        
        with pytest.raises(requests.exceptions.HTTPError):
            client._make_request("test/endpoint")
    
    @patch('requests.Session.get')
    def test_rate_limiting(self, mock_get):
        """Test rate limiting functionality."""
        mock_response = Mock()
        mock_response.json.return_value = {"test": "data"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        client = BaseAPIClient("https://api.nasa.gov", "test_key")
        
        # Make two requests quickly
        client._make_request("test/endpoint1")
        client._make_request("test/endpoint2")
        
        # Should have made two calls
        assert mock_get.call_count == 2


class TestNASAClient:
    """Test NASA API client."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = NASAClient()
    
    @patch.object(NASAClient, '_make_request')
    def test_get_apod_single_date(self, mock_request):
        """Test getting APOD for single date."""
        mock_request.return_value = {
            "date": "2024-01-01",
            "title": "Test APOD",
            "explanation": "Test explanation",
            "url": "https://example.com/image.jpg",
            "media_type": "image"
        }
        
        result = self.client.get_apod(date="2024-01-01")
        
        mock_request.assert_called_once_with("planetary/apod", {"date": "2024-01-01"})
        assert result["title"] == "Test APOD"
    
    @patch.object(NASAClient, '_make_request')
    def test_get_mars_rover_photos(self, mock_request):
        """Test getting Mars rover photos."""
        mock_request.return_value = {
            "photos": [
                {
                    "id": 123,
                    "sol": 1000,
                    "camera": {"id": 1, "name": "FHAZ", "rover_id": 5, "full_name": "Front Hazard Avoidance Camera"},
                    "img_src": "https://example.com/photo.jpg",
                    "earth_date": "2024-01-01",
                    "rover": {"id": 5, "name": "curiosity", "landing_date": "2012-08-05", "launch_date": "2011-11-26", "status": "active"}
                }
            ]
        }
        
        result = self.client.get_mars_rover_photos(rover="curiosity", sol=1000)
        
        mock_request.assert_called_once_with(
            "mars-photos/api/v1/rovers/curiosity/photos",
            {"page": 1, "sol": 1000}
        )
        assert len(result["photos"]) == 1
        assert result["photos"][0]["id"] == 123
    
    @patch.object(NASAClient, '_make_request')
    def test_get_neo_feed(self, mock_request):
        """Test getting NEO feed."""
        mock_request.return_value = {
            "near_earth_objects": {
                "2024-01-01": [
                    {
                        "id": "123",
                        "neo_reference_id": "123",
                        "name": "Test NEO",
                        "nasa_jpl_url": "https://example.com",
                        "absolute_magnitude_h": 20.5,
                        "estimated_diameter": {
                            "kilometers": {"estimated_diameter_min": 0.1, "estimated_diameter_max": 0.2}
                        },
                        "is_potentially_hazardous_asteroid": False,
                        "close_approach_data": []
                    }
                ]
            }
        }
        
        result = self.client.get_neo_feed(start_date="2024-01-01", end_date="2024-01-01")
        
        mock_request.assert_called_once_with(
            "neo/rest/v1/feed",
            {"start_date": "2024-01-01", "end_date": "2024-01-01"}
        )
        assert "near_earth_objects" in result


class TestAPIUtils:
    """Test API utility functions."""
    
    def test_validate_date_format_valid(self):
        """Test valid date format validation."""
        assert validate_date_format("2024-01-01") is True
        assert validate_date_format("2023-12-31") is True
    
    def test_validate_date_format_invalid(self):
        """Test invalid date format validation."""
        assert validate_date_format("2024-1-1") is False
        assert validate_date_format("01-01-2024") is False
        assert validate_date_format("invalid") is False
    
    def test_parse_apod_response_single(self):
        """Test parsing single APOD response."""
        response_data = {
            "date": "2024-01-01",
            "title": "Test APOD",
            "explanation": "Test explanation",
            "url": "https://example.com/image.jpg",
            "media_type": "image"
        }
        
        result = parse_apod_response(response_data)
        
        assert len(result) == 1
        assert result[0].title == "Test APOD"
        assert result[0].date == date(2024, 1, 1)
    
    def test_parse_apod_response_list(self):
        """Test parsing list of APOD responses."""
        response_data = [
            {
                "date": "2024-01-01",
                "title": "Test APOD 1",
                "explanation": "Test explanation 1",
                "url": "https://example.com/image1.jpg",
                "media_type": "image"
            },
            {
                "date": "2024-01-02",
                "title": "Test APOD 2",
                "explanation": "Test explanation 2",
                "url": "https://example.com/image2.jpg",
                "media_type": "image"
            }
        ]
        
        result = parse_apod_response(response_data)
        
        assert len(result) == 2
        assert result[0].title == "Test APOD 1"
        assert result[1].title == "Test APOD 2"


@pytest.fixture
def mock_nasa_client():
    """Fixture for mocked NASA client."""
    with patch('src.api.nasa_client.NASAClient') as mock:
        yield mock


class TestIntegration:
    """Integration tests for API clients."""
    
    @pytest.mark.integration
    def test_nasa_client_health_check(self):
        """Test NASA client health check (requires internet)."""
        client = NASAClient()
        # This test requires actual internet connection
        # In a real environment, you might want to skip this or use a test API
        try:
            health = client.health_check()
            assert isinstance(health, bool)
        except Exception:
            pytest.skip("Internet connection required for integration test")
    
    @pytest.mark.integration
    def test_get_apod_real_api(self):
        """Test getting real APOD data (requires internet)."""
        client = NASAClient()
        try:
            result = client.get_apod(count=1)
            assert isinstance(result, (dict, list))
        except Exception:
            pytest.skip("Internet connection required for integration test")
