# NASA API Pipeline - Deployment Guide

This guide provides detailed instructions for deploying the NASA API Pipeline in different environments.

## 🚀 Quick Deployment (Recommended)

### Prerequisites
- Windows 10 with 6GB+ RAM
- Docker Desktop installed and running
- Git installed
- Python 3.11+ (optional, for local development)

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository-url>
cd NASA_API_Pipeline

# Run the setup script
bash scripts/setup.sh
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configurations
# At minimum, add your NASA API key:
NASA_API_KEY=your_nasa_api_key_here
```

### 3. Deploy with Docker Compose
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### 4. Access Applications
- **Airflow Web UI**: http://localhost:8080 (admin/admin)
- **Streamlit Dashboard**: http://localhost:8501
- **PostgreSQL**: localhost:5432

### 5. Initialize and Run Pipeline
```bash
# Initialize database (if not done automatically)
uv run main.py setup

# Run the pipeline
uv run main.py pipeline
```

## 🔧 Development Deployment

For development with hot reloading and additional tools:

```bash
# Start development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Access Jupyter Lab for development
# http://localhost:8888
```

## 📊 Production Deployment

### Resource Requirements
- **Minimum**: 6GB RAM, 2 CPU cores, 20GB storage
- **Recommended**: 8GB RAM, 4 CPU cores, 50GB storage

### Memory Allocation (6GB System)
- PostgreSQL: 768MB
- Airflow Webserver: 1.5GB
- Airflow Scheduler: 1.2GB
- Streamlit: 768MB
- Redis: 128MB
- System/OS: ~2.6GB

### Production Configuration

1. **Update Environment Variables**:
```bash
# Production .env settings
POSTGRES_PASSWORD=secure_production_password
AIRFLOW__WEBSERVER__SECRET_KEY=your_secure_secret_key
AIRFLOW__CORE__FERNET_KEY=your_fernet_key

# Generate Fernet key:
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
```

2. **SSL/TLS Configuration** (Optional):
```bash
# Add SSL certificates to nginx or reverse proxy
# Update docker-compose.yml with SSL ports
```

3. **Backup Configuration**:
```bash
# Add volume mounts for persistent data
# Configure automated database backups
```

## 🐳 Docker Configuration Details

### Service Dependencies
```
postgres (database)
├── airflow-webserver
├── airflow-scheduler
└── streamlit
```

### Volume Mounts
- `./dags:/opt/airflow/dags` - Airflow DAGs
- `./logs:/opt/airflow/logs` - Airflow logs
- `./data:/opt/airflow/data` - Data storage
- `postgres_data:/var/lib/postgresql/data` - Database data

### Health Checks
All services include health checks for reliable startup:
- PostgreSQL: `pg_isready`
- Redis: `redis-cli ping`
- Airflow: HTTP health endpoint
- Streamlit: Process check

## 🔍 Monitoring and Logging

### Application Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f airflow-webserver
docker-compose logs -f postgres
docker-compose logs -f streamlit
```

### Log Locations
- Airflow logs: `./logs/`
- Application logs: Docker container logs
- Database logs: PostgreSQL container logs

### Monitoring Endpoints
- Airflow health: http://localhost:8080/health
- Database health: Check via pipeline health endpoint
- API health: Monitored through pipeline

## 🚨 Troubleshooting

### Common Issues

1. **Memory Issues**
```bash
# Check Docker memory usage
docker stats

# Reduce memory limits in docker-compose.yml if needed
# Restart services
docker-compose restart
```

2. **Port Conflicts**
```bash
# Check port usage
netstat -an | findstr :8080
netstat -an | findstr :5432

# Change ports in docker-compose.yml if needed
```

3. **Database Connection Issues**
```bash
# Check PostgreSQL status
docker-compose exec postgres pg_isready -U airflow

# Reset database
docker-compose down -v
docker-compose up postgres -d
```

4. **API Rate Limits**
```bash
# Get NASA API key for higher limits
# Update .env with your API key
NASA_API_KEY=your_api_key_here
```

### Performance Optimization

1. **For 6GB RAM Systems**:
   - Use LocalExecutor instead of CeleryExecutor
   - Limit concurrent DAG runs
   - Reduce Airflow worker processes

2. **Database Optimization**:
   - Regular VACUUM and ANALYZE
   - Monitor query performance
   - Index optimization

3. **API Optimization**:
   - Implement caching
   - Batch API requests
   - Respect rate limits

## 🔄 Updates and Maintenance

### Updating the Application
```bash
# Pull latest changes
git pull origin main

# Rebuild containers
docker-compose build

# Restart services
docker-compose down
docker-compose up -d
```

### Database Maintenance
```bash
# Backup database
docker-compose exec postgres pg_dump -U airflow airflow > backup.sql

# Restore database
docker-compose exec -T postgres psql -U airflow airflow < backup.sql
```

### Cleaning Up
```bash
# Remove old containers and images
docker system prune -a

# Remove volumes (WARNING: This deletes all data)
docker-compose down -v
```

## 🔐 Security Considerations

### Production Security
1. Change default passwords
2. Use environment variables for secrets
3. Enable SSL/TLS
4. Restrict network access
5. Regular security updates

### API Security
1. Secure NASA API key storage
2. Implement rate limiting
3. Monitor API usage
4. Use HTTPS endpoints

## 📈 Scaling

### Horizontal Scaling
- Use CeleryExecutor with Redis
- Add worker nodes
- Load balance Streamlit instances

### Vertical Scaling
- Increase memory allocation
- Add CPU cores
- Optimize database configuration

## 🆘 Support

### Getting Help
1. Check logs for error messages
2. Review troubleshooting section
3. Check GitHub issues
4. Create new issue with:
   - Error messages
   - System specifications
   - Steps to reproduce

### Useful Commands
```bash
# Check system resources
docker stats
docker system df

# Service management
docker-compose ps
docker-compose restart <service>
docker-compose logs <service>

# Database access
docker-compose exec postgres psql -U airflow

# Application shell
docker-compose exec airflow-webserver bash
```
