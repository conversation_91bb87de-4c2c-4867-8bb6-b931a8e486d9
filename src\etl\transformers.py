"""Data transformers for NASA API pipeline."""

import logging
import re
from datetime import datetime, date
from typing import Dict, List, Any, Optional
import pandas as pd
from decimal import Decimal


class DataTransformer:
    """Base data transformer class."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def clean_text(self, text: Optional[str]) -> Optional[str]:
        """Clean and normalize text data."""
        if not text:
            return None
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters that might cause issues
        text = re.sub(r'[^\w\s\-.,!?():]', '', text)
        
        return text
    
    def validate_url(self, url: Optional[str]) -> Optional[str]:
        """Validate and clean URL."""
        if not url:
            return None
        
        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if url_pattern.match(url):
            return url
        else:
            self.logger.warning(f"Invalid URL detected: {url}")
            return None


class APODTransformer(DataTransformer):
    """APOD data transformer."""
    
    def transform(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform APOD data."""
        self.logger.info(f"Transforming {len(raw_data)} APOD records")
        
        transformed_data = []
        for item in raw_data:
            try:
                transformed_item = {
                    'date': item['date'],
                    'title': self.clean_text(item.get('title', '')),
                    'explanation': self.clean_text(item.get('explanation', '')),
                    'url': self.validate_url(item.get('url')),
                    'hdurl': self.validate_url(item.get('hdurl')),
                    'media_type': item.get('media_type', 'image').lower(),
                    'copyright': self.clean_text(item.get('copyright')),
                    'service_version': item.get('service_version')
                }
                
                # Validate required fields
                if not transformed_item['title'] or not transformed_item['url']:
                    self.logger.warning(f"Skipping APOD record with missing required fields: {item.get('date')}")
                    continue
                
                # Ensure title length doesn't exceed database limit
                if len(transformed_item['title']) > 500:
                    transformed_item['title'] = transformed_item['title'][:497] + "..."
                
                # Ensure copyright length doesn't exceed database limit
                if transformed_item['copyright'] and len(transformed_item['copyright']) > 200:
                    transformed_item['copyright'] = transformed_item['copyright'][:197] + "..."
                
                transformed_data.append(transformed_item)
                
            except Exception as e:
                self.logger.error(f"Failed to transform APOD record: {e}")
                continue
        
        self.logger.info(f"Successfully transformed {len(transformed_data)} APOD records")
        return transformed_data
    
    def calculate_quality_score(self, data: List[Dict[str, Any]]) -> float:
        """Calculate data quality score for APOD data."""
        if not data:
            return 0.0
        
        total_score = 0
        for item in data:
            score = 0
            
            # Check for required fields
            if item.get('title'): score += 25
            if item.get('explanation'): score += 25
            if item.get('url'): score += 25
            if item.get('date'): score += 25
            
            # Bonus points for optional fields
            if item.get('hdurl'): score += 5
            if item.get('copyright'): score += 5
            
            total_score += min(score, 100)  # Cap at 100
        
        return total_score / len(data)


class MarsRoverTransformer(DataTransformer):
    """Mars rover data transformer."""
    
    def transform_photos(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform Mars rover photo data."""
        self.logger.info(f"Transforming {len(raw_data)} Mars rover photos")
        
        transformed_data = []
        for item in raw_data:
            try:
                transformed_item = {
                    'photo_id': int(item['photo_id']),
                    'sol': int(item['sol']) if item.get('sol') is not None else None,
                    'earth_date': item['earth_date'],
                    'img_src': self.validate_url(item.get('img_src')),
                    'rover_id': int(item['rover_id']),
                    'camera_id': int(item['camera_id'])
                }
                
                # Validate required fields
                if not transformed_item['img_src'] or not transformed_item['photo_id']:
                    self.logger.warning(f"Skipping photo with missing required fields: {item.get('photo_id')}")
                    continue
                
                transformed_data.append(transformed_item)
                
            except Exception as e:
                self.logger.error(f"Failed to transform rover photo: {e}")
                continue
        
        self.logger.info(f"Successfully transformed {len(transformed_data)} rover photos")
        return transformed_data
    
    def transform_rover_manifest(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform rover manifest data."""
        self.logger.info(f"Transforming rover manifest for {raw_data.get('name')}")
        
        try:
            transformed_data = {
                'id': int(raw_data['id']),
                'name': raw_data['name'].lower(),
                'landing_date': raw_data['landing_date'],
                'launch_date': raw_data['launch_date'],
                'status': raw_data['status'].lower(),
                'max_sol': int(raw_data['max_sol']) if raw_data.get('max_sol') else None,
                'max_date': raw_data.get('max_date'),
                'total_photos': int(raw_data['total_photos']) if raw_data.get('total_photos') else 0
            }
            
            self.logger.info(f"Successfully transformed rover manifest for {transformed_data['name']}")
            return transformed_data
            
        except Exception as e:
            self.logger.error(f"Failed to transform rover manifest: {e}")
            raise


class NEOTransformer(DataTransformer):
    """NEO data transformer."""
    
    def transform(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform NEO data."""
        self.logger.info(f"Transforming {len(raw_data)} NEO records")
        
        transformed_data = []
        for item in raw_data:
            try:
                # Transform main NEO data
                transformed_item = {
                    'neo_id': str(item['neo_id']),
                    'neo_reference_id': str(item['neo_reference_id']),
                    'name': self.clean_text(item.get('name', '')),
                    'nasa_jpl_url': self.validate_url(item.get('nasa_jpl_url')),
                    'absolute_magnitude_h': self._safe_decimal(item.get('absolute_magnitude_h')),
                    'estimated_diameter_min_km': self._safe_decimal(item.get('estimated_diameter_min_km')),
                    'estimated_diameter_max_km': self._safe_decimal(item.get('estimated_diameter_max_km')),
                    'is_potentially_hazardous': bool(item.get('is_potentially_hazardous', False)),
                    'is_sentry_object': bool(item.get('is_sentry_object', False)),
                    'close_approach_data': []
                }
                
                # Transform close approach data
                for approach in item.get('close_approach_data', []):
                    transformed_approach = {
                        'close_approach_date': approach['close_approach_date'],
                        'close_approach_date_full': approach.get('close_approach_date_full'),
                        'epoch_date_close_approach': int(approach['epoch_date_close_approach']) if approach.get('epoch_date_close_approach') else None,
                        'relative_velocity_kmh': self._safe_decimal(approach.get('relative_velocity_kmh')),
                        'relative_velocity_kms': self._safe_decimal(approach.get('relative_velocity_kms')),
                        'miss_distance_km': self._safe_decimal(approach.get('miss_distance_km')),
                        'miss_distance_lunar': self._safe_decimal(approach.get('miss_distance_lunar')),
                        'miss_distance_au': self._safe_decimal(approach.get('miss_distance_au')),
                        'orbiting_body': approach.get('orbiting_body', 'Earth')
                    }
                    transformed_item['close_approach_data'].append(transformed_approach)
                
                # Validate required fields
                if not transformed_item['neo_id'] or not transformed_item['name']:
                    self.logger.warning(f"Skipping NEO with missing required fields: {item.get('neo_id')}")
                    continue
                
                # Ensure name length doesn't exceed database limit
                if len(transformed_item['name']) > 200:
                    transformed_item['name'] = transformed_item['name'][:197] + "..."
                
                transformed_data.append(transformed_item)
                
            except Exception as e:
                self.logger.error(f"Failed to transform NEO record: {e}")
                continue
        
        self.logger.info(f"Successfully transformed {len(transformed_data)} NEO records")
        return transformed_data
    
    def _safe_decimal(self, value: Any) -> Optional[Decimal]:
        """Safely convert value to Decimal."""
        if value is None:
            return None
        
        try:
            return Decimal(str(value))
        except (ValueError, TypeError):
            self.logger.warning(f"Could not convert {value} to Decimal")
            return None
    
    def calculate_hazard_score(self, neo_data: Dict[str, Any]) -> float:
        """Calculate hazard score for NEO."""
        score = 0.0
        
        # Base hazard score
        if neo_data.get('is_potentially_hazardous'):
            score += 50.0
        
        # Size factor
        max_diameter = neo_data.get('estimated_diameter_max_km', 0)
        if max_diameter:
            if max_diameter > 1.0:  # > 1km
                score += 30.0
            elif max_diameter > 0.5:  # > 500m
                score += 20.0
            elif max_diameter > 0.1:  # > 100m
                score += 10.0
        
        # Distance factor
        min_distance = float('inf')
        for approach in neo_data.get('close_approach_data', []):
            distance = approach.get('miss_distance_km', float('inf'))
            if distance and distance < min_distance:
                min_distance = distance
        
        if min_distance != float('inf'):
            if min_distance < 1000000:  # < 1M km
                score += 20.0
            elif min_distance < 5000000:  # < 5M km
                score += 10.0
            elif min_distance < 10000000:  # < 10M km
                score += 5.0
        
        return min(score, 100.0)  # Cap at 100


class DataQualityChecker:
    """Data quality checker for all data types."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def check_data_completeness(self, data: List[Dict[str, Any]], required_fields: List[str]) -> Dict[str, Any]:
        """Check data completeness."""
        if not data:
            return {'completeness_score': 0.0, 'missing_fields': required_fields}
        
        total_records = len(data)
        field_completeness = {}
        
        for field in required_fields:
            complete_count = sum(1 for record in data if record.get(field) is not None)
            field_completeness[field] = (complete_count / total_records) * 100
        
        overall_score = sum(field_completeness.values()) / len(required_fields)
        missing_fields = [field for field, score in field_completeness.items() if score < 100]
        
        return {
            'completeness_score': overall_score,
            'field_completeness': field_completeness,
            'missing_fields': missing_fields,
            'total_records': total_records
        }
    
    def check_data_validity(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check data validity."""
        if not data:
            return {'validity_score': 0.0, 'invalid_records': 0}
        
        invalid_count = 0
        issues = []
        
        for i, record in enumerate(data):
            record_issues = []
            
            # Check for empty required string fields
            for key, value in record.items():
                if isinstance(value, str) and not value.strip():
                    record_issues.append(f"Empty string in field '{key}'")
                elif value is None and key in ['id', 'name', 'date']:
                    record_issues.append(f"Null value in required field '{key}'")
            
            if record_issues:
                invalid_count += 1
                issues.append(f"Record {i}: {', '.join(record_issues)}")
        
        validity_score = ((len(data) - invalid_count) / len(data)) * 100
        
        return {
            'validity_score': validity_score,
            'invalid_records': invalid_count,
            'total_records': len(data),
            'issues': issues[:10]  # Limit to first 10 issues
        }
