#!/bin/bash

# NASA API Pipeline Setup Script
# This script sets up the development environment

set -e

echo "🚀 NASA API Pipeline Setup"
echo "=========================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

echo "✅ Docker is running"

# Check if UV is installed
if ! command -v uv &> /dev/null; then
    echo "📦 Installing UV package manager..."
    pip install uv
fi

echo "✅ UV package manager is available"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your NASA API key and other configurations"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p data logs notebooks

# Install Python dependencies
echo "📦 Installing Python dependencies..."
uv sync

# Build Docker images
echo "🐳 Building Docker images..."
docker-compose build

# Start PostgreSQL for initial setup
echo "🗄️  Starting PostgreSQL..."
docker-compose up postgres -d

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Initialize database
echo "🗄️  Initializing database..."
uv run main.py setup

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your NASA API key"
echo "2. Start all services: docker-compose up -d"
echo "3. Access applications:"
echo "   - Airflow: http://localhost:8080 (admin/admin)"
echo "   - Streamlit: http://localhost:8501"
echo "   - PostgreSQL: localhost:5432"
echo ""
echo "4. Run the pipeline: uv run main.py pipeline"
echo ""
echo "For development:"
echo "- Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up"
echo "- Jupyter Lab will be available at: http://localhost:8888"
